﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lotto
{
    class SenderOfHistoricalKeno : SenderOfHistorical
    {
        internal SenderOfHistoricalKeno(LotteryKeno lottery, bool itIsThePresent):base(lottery, itIsThePresent)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            grading = new MessagesBuffer<TypedMessage>($"{Integration.Kafka.TopicForLottoGrading}_keno", RuntimeSettings.BLOCK_SIZE_LOTTO, itIsThePresent);
        }

        internal override void WriteLoserData(DateTime date, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!(ticket.IsLoser() || ticket.IsRegraded())) throw new GameEngineException($"Ticket is in {ticket.Prizing} and it is supposed to be loser ticket");
            if (ticket.TicketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var profit = !ticket.IsRegraded() ? ticket.Profit() : 0m;
                var draw = (LotteryDrawKeno)ticket.Draw;
                var drawId = ((TicketKeno)ticket).DrawIdPrefix;
                var info = new LoserKenoInfo(
                    company: this.lottery.Company,
                    draw: draw.GetNumbersWithMultiplierAndBulleye(),
                    gradedBy: draw.WhoGraded,
                    hour: date.Hour,
                    minute: date.Minute,
                    year: date.Year,
                    month: date.Month,
                    day: date.Day,
                    accountNumber: ticket.Player.AccountNumber,
                    ticket: ticket.AsString(),
                    amount: ticket.BetAmount(),
                    action: ticket.Grading,
                    drawingId: int.Parse(drawId),
                    creation: ticket.CreationDate,
                    orderNumber: ticket.Order.Number,
                    ticketNumber: ticket.TicketNumber,
                    profit: profit,
                    prizesVersion: ticket.Prizes.VersionNumber,
                    domainId: ticket.DomainId,
                    domainUrl: string.Empty,
                    currencyId: Coinage.Coin(ticket.Order.CurrencyCode).Id
                );

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal void WriteLoserData(LoserKenoInfo commonInfo, TicketKeno ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!ticket.IsLoser()) throw new GameEngineException($"Ticket is in {ticket.Prizing} and it is supposed to be loser ticket");
            var ticketNumber = ticket.TicketNumber;
            if (ticketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var info = new LoserKenoInfo(commonInfo);
                info.SetValuesFromTicket(ticket);

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal override void WriteWinnerData(DateTime date, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!ticket.IsWinner()) throw new GameEngineException($"Ticket is in {ticket.Prizing} and it is supposed to be winner ticket");
            if (ticket.TicketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var draw = (LotteryDrawKeno)ticket.Draw;
                var ticketKeno = (TicketKeno)ticket;
                var drawId = ticketKeno.DrawIdPrefix;
                var info = new WinnerKenoInfo(
                    company: this.lottery.Company,
                    draw: draw.GetNumbersWithMultiplierAndBulleye(),
                    gradedBy: draw.WhoGraded,
                    prize: ticket.CalculatedPrize(),
                    bulleyePrize: ticketKeno.CalculateBulleyePrizeCriteria(),
                    hour: date.Hour,
                    minute: date.Minute,
                    year: date.Year,
                    month: date.Month,
                    day: date.Day,
                    accountNumber: ticket.Player.AccountNumber,
                    ticket: ticket.AsString(),
                    amount: ticket.BetAmount(),
                    action: ticket.Grading,
                    drawingId: int.Parse(drawId),
                    creation: ticket.CreationDate,
                    orderNumber: ticket.Order.Number,
                    ticketNumber: ticket.TicketNumber,
                    profit: ticket.TicketAmount()- ticket.CalculatedPrize(),
                    prizesVersion: ticket.Prizes.VersionNumber,
                    domainId: ticket.DomainId,
                    domainUrl: string.Empty,
                    currencyId: Coinage.Coin(ticket.Order.CurrencyCode).Id
                );

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal override void WriteNoActionData(DateTime date, Ticket ticket)
        {
            if (!itIsThePresent) return;

            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!ticket.IsNoAction()) throw new GameEngineException($"Ticket is in {ticket.Grading} and it is supposed to be {GameboardStatus.NOACTION} when it is marked as no action");
            if (ticket.TicketNumber != Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER)
            {
                var noAction = lottery.GetLotteryNoAction(date);
                var drawId = ((TicketKeno)ticket).DrawIdPrefix;
                var info = new NoActionKenoInfo(
                    company: this.lottery.Company,
                    noActionBy: !string.IsNullOrWhiteSpace(noAction.WhoSetNoAction) ? noAction.WhoSetNoAction : noAction.WhoSetAction,
                    hour: date.Hour,
                    minute: date.Minute,
                    year: date.Year,
                    month: date.Month,
                    day: date.Day,
                    accountNumber: ticket.Player.AccountNumber,
                    ticket: ticket.AsString(),
                    amount: ticket.BetAmount(),
                    action: ticket.Grading,
                    drawingId: int.Parse(drawId),
                    creation: ticket.CreationDate,
                    orderNumber: ticket.Order.Number,
                    ticketNumber: ticket.TicketNumber,
                    prizesVersion: ticket.Prizes.VersionNumber,
                    domainId: ticket.DomainId,
                    domainUrl: string.Empty,
                    currencyId: Coinage.Coin(ticket.Order.CurrencyCode).Id
                );

                grading.Add(info);
                ticket.IsMarkedAsSent = true;
            }
        }

        internal void WriteGeneralTicketData(int drawingId, DateTime drawDate, string draw, string drawingDescription, IEnumerable<Domain> domains)
        {
            if (drawingId <= 0) throw new GameEngineException("Invalid draw id");
            if (string.IsNullOrWhiteSpace(draw)) throw new ArgumentNullException(nameof(draw));
            if (string.IsNullOrWhiteSpace(drawingDescription)) throw new ArgumentNullException(nameof(drawingDescription));
            if (domains == null) throw new GameEngineException("Domains should contains at least one element");

            WinnerResultsInfo winnerDrawings = new WinnerResultsInfo(GradeTicketType.WINNER_RESULTS, draw, LotteryDraw.WITHOUT_FIREBALL, drawingId, drawDate.Year, drawDate.Month, drawDate.Day, drawDate.Hour, drawDate.Minute);
            this.grading.Add(winnerDrawings);
            DrawingMessage drawingMsg = new DrawingMessage(GradeTicketType.DRAWINGS, drawingId, drawingId, drawingDescription, IdOfLottery.KN, TypeNumberSequence.ENDING);
            this.grading.Add(drawingMsg);
            foreach (var domain in domains)
            {
                var domainsMsg = new DomainBIMessage(domain.Id, domain.Url, domain.Alias);
                this.grading.Add(domainsMsg);
            }
        }

        internal void StartSending(DateTime drawDate, int drawingId, string gradedBy, string draw)
        {
            var headerInfo = new TicketsStreamStartingMessage(GradeTicketType.STARTING_TICKET, "-", drawDate, IdOfLottery.KN, drawingId, gradedBy, draw, LotteryDraw.WITHOUT_FIREBALL);
            this.grading.Add(headerInfo);
        }

        internal void EndSending(DateTime drawDate)
        {
            var headerInfo = new TicketsStreamEndingMessage(GradeTicketType.ENDING_TICKET, "-", drawDate, IdOfLottery.KN);
            this.grading.Add(headerInfo);
        }
    }

    public abstract class TicketKenoMessage : TicketMessage
    {
        public TicketKenoMessage(string message) : base(message)
        {

        }

        internal TicketKenoMessage(int hour, int minute, int year, int month, int day, string accountNumber, string ticket, decimal amount, GameboardStatus action, int drawingId, DateTime creation, int orderNumber, int ticketNumber, 
            int prizesVersion, int domainId, string domainUrl, int currencyId, GradeTicketType gradeType)
            : base(hour, minute, year, month, day, accountNumber, ticket, amount, action, drawingId, creation, orderNumber, ticketNumber, prizesVersion, domainId, domainUrl, currencyId, gradeType)
        {
        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);
            
            DomainId = int.Parse(serializatedMessage[fieldOrder++]);
            this.PrizesVersion = int.Parse(serializatedMessage[fieldOrder++]);
            this.DrawingId = int.Parse(serializatedMessage[fieldOrder++]);
            this.Year = int.Parse(serializatedMessage[fieldOrder++]);
            this.Month = int.Parse(serializatedMessage[fieldOrder++]);
            this.Day = int.Parse(serializatedMessage[fieldOrder++]);
            this.Hour = int.Parse(serializatedMessage[fieldOrder++]);
            this.Minute = int.Parse(serializatedMessage[fieldOrder++]);
            this.AccountNumber = serializatedMessage[fieldOrder++];
            this.Ticket = serializatedMessage[fieldOrder++];
            this.Amount = decimal.Parse(serializatedMessage[fieldOrder++]);
            this.Action = (GameboardStatus)int.Parse(serializatedMessage[fieldOrder++]);
            this.Creation = new DateTime(
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++]),
                int.Parse(serializatedMessage[fieldOrder++])
                );
            this.TicketNumber = int.Parse(serializatedMessage[fieldOrder++]);
            CurrencyId = int.Parse(serializatedMessage[fieldOrder++]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DomainId).
            AddProperty(this.PrizesVersion);
            AddProperty(this.DrawingId).
            AddProperty(this.Year).
            AddProperty(this.Month).
            AddProperty(this.Day).
            AddProperty(this.Hour).
            AddProperty(this.Minute).
            AddProperty(this.AccountNumber).
            AddProperty(this.Ticket).
            AddProperty(this.Amount).
            AddProperty((int)this.Action).
            AddProperty(this.Creation).
            AddProperty(this.Creation.Millisecond).
            AddProperty(this.TicketNumber.ToString()).
            AddProperty(CurrencyId);
        }

        internal IdOfLottery IdOfLottery
        {
            get
            {
                return IdOfLottery.KN;
            }
        }

        internal override string GameType()
        {
            var ticketId = TicketType;
            switch (ticketId)
            {
                case TicketType.K10:
                    return "TicketKeno10Single";
                case TicketType.K12:
                    return "TicketKeno12Single";
                default:
                    throw new Exception($"The ticket id {ticketId} is not valid.");
            }
        }

        internal override string GameTypeLottery()
        {
            var ticketId = TicketType;
            switch (ticketId)
            {
                case TicketType.K10:
                case TicketType.K12:
                    return "LotteryKeno";
                default:
                    throw new Exception($"The ticket id {ticketId} is not valid.");
            }
        }

        internal override decimal TicketAmount()
        {
            return Amount;
        }
    }

    public sealed class WinnerKenoInfo : TicketKenoMessage
    {
        internal string Draw { get; private set; }
        internal string GradedBy { get; private set; }
        internal decimal Prize { get; private set; }
        internal decimal BulleyePrize { get; private set; }
        internal decimal Profit { get; private set; }
        internal int AffiliateId { get; set; }

        public WinnerKenoInfo(string message) : base(message)
        {

        }

        internal WinnerKenoInfo(Company company, string draw, string gradedBy, decimal prize, decimal bulleyePrize, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, decimal amount, GameboardStatus action,
            int drawingId, DateTime creation, int orderNumber, int ticketNumber, decimal profit, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(hour, minute, year, month, day, accountNumber, ticket, amount, action, drawingId, creation, orderNumber, ticketNumber, prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.WINNER_TICKET)
        {
            this.Company = company;
            this.Draw = draw;
            this.GradedBy = gradedBy;
            this.Prize = prize;
            this.BulleyePrize = bulleyePrize;
            this.Profit = profit;
            ValidateFields();
        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);
            this.Company = null;
            
            this.Draw = serializatedMessage[fieldOrder++];
            this.GradedBy = serializatedMessage[fieldOrder++];
            this.Prize = decimal.Parse(serializatedMessage[fieldOrder++]);
            BulleyePrize = decimal.Parse(serializatedMessage[fieldOrder++]);
            Profit = decimal.Parse(serializatedMessage[fieldOrder++]);
            ValidateFields();
        }

        private void ValidateFields()
        {
            if (String.IsNullOrWhiteSpace(this.Draw)) throw new ArgumentNullException("Draw can not be null");
            if (String.IsNullOrWhiteSpace(this.GradedBy)) throw new ArgumentNullException("Graded by can not be null");
            if (Prize < 0) throw new GameEngineException("Prize must be upper than zero");
        }

        internal bool WasPurchasedForFree
        {
            get
            {
                var result = Profit == -Prize;
                return result;
            }
        }

        internal bool HasSequenceOfNumbers
        {
            get
            {
                var result = !Draw.Contains(LotteryDraw.DEFAULT_VALUE_FOR_EMPTY_WINNER_NUMBER);
                return result;
            }
        }

        internal decimal WonAmount()
        {
            return Amount * Prize;
        }

        internal bool IsWinner()
        {
            return Prize > 0;
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(this.Draw).
            AddProperty(this.GradedBy).
            AddProperty(this.Prize).
            AddProperty(BulleyePrize).
            AddProperty(Profit);
        }
    }

    public sealed class LoserKenoInfo : TicketKenoMessage
    {
        internal string Draw { get; private set; }
        internal string GradedBy { get; private set; }
        internal decimal Profit { get; private set; }


        internal LoserKenoInfo(Company company, string draw, string gradedBy, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, decimal amount, GameboardStatus action,
            int drawingId, DateTime creation, int orderNumber, int ticketNumber, decimal profit, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(hour, minute, year, month, day, accountNumber, ticket, amount, action, drawingId, creation, orderNumber, ticketNumber, prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.LOSER_TICKET)
        {
            this.Company = company;
            this.Draw = draw;
            this.GradedBy = gradedBy;
            this.Profit = profit;
            ValidateFields();
        }

        public LoserKenoInfo(string message) : base(message)
        {

        }

        public LoserKenoInfo(int hour, int minute, int year, int month, int day, string draw, string gradedBy, int drawingId) :
            base(hour, minute, year, month, day, string.Empty, string.Empty, 0, GameboardStatus.UNKNOWN, drawingId, DateTime.MinValue, 0, 0, 0, 0, string.Empty, Coinage.Coin("USD").Id, GradeTicketType.TEMP_TICKET)
        {
            Draw = draw;
            GradedBy = gradedBy;
        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);
            this.Company = null;
            this.Draw = serializatedMessage[fieldOrder++];
            this.GradedBy = serializatedMessage[fieldOrder++];
            Profit = decimal.Parse(serializatedMessage[fieldOrder++]);
            ValidateFields();
        }

        private void ValidateFields()
        {
            if (String.IsNullOrWhiteSpace(this.Draw)) throw new ArgumentNullException("Draw can not be null");
            if (String.IsNullOrWhiteSpace(this.GradedBy)) throw new ArgumentNullException("Graded by can not be null");
        }

        internal bool WasPurchasedForFree
        {
            get
            {
                var result = Profit == 0m;
                return result;
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(this.Draw).
            AddProperty(this.GradedBy).
            AddProperty(Profit);
        }

        internal LoserKenoInfo(LoserKenoInfo commonInfo) :
            base(commonInfo.Hour, commonInfo.Minute, commonInfo.Year, commonInfo.Month, commonInfo.Day, string.Empty, string.Empty, 0, GameboardStatus.UNKNOWN, commonInfo.DrawingId, DateTime.MinValue, 0, 0, 0, 0, string.Empty, Coinage.Coin("USD").Id, GradeTicketType.LOSER_TICKET)
        {
            this.Draw = commonInfo.Draw;
            this.GradedBy = commonInfo.GradedBy;
        }

        internal override void SetValuesFromTicket(Ticket ticket)
        {
            base.SetValuesFromTicket(ticket);
            Profit = ticket.Profit();
        }
    }

    public sealed class NoActionKenoInfo : TicketKenoMessage
    {
        internal string NoActionBy { get; private set; }

        internal NoActionKenoInfo(Company company, string noActionBy, int hour, int minute, int year, int month, int day, string accountNumber, string ticket, decimal amount, GameboardStatus action,
            int drawingId, DateTime creation, int orderNumber, int ticketNumber, int prizesVersion, int domainId, string domainUrl, int currencyId) :
            base(hour, minute, year, month, day, accountNumber, ticket, amount, action, drawingId, creation, orderNumber, ticketNumber, prizesVersion, domainId, domainUrl, currencyId, GradeTicketType.NOACTION_TICKET)
        {
            this.Company = company;
            this.NoActionBy = noActionBy;
        }

        public NoActionKenoInfo(string message) : base(message)
        {

        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);
            this.Company = null;
            this.NoActionBy = serializatedMessage[fieldOrder++];
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(this.NoActionBy);
        }
    }

}