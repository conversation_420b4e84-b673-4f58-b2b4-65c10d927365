﻿using ClickHouse.Client.ADO;
using ClickHouse.Client.Copy;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public interface IStorageManager
    {
        void CreateTablesIfNotExists(string kind);
        void DropAllTables(string kind);
    }

    public interface IOlapSchemaManager : IStorageManager { }

    public interface IStorageRepository
    {
        void CreateDeposit(string kind, Deposit depositDetails);
        void CreateJar(string kind, long version, string description, DateTime created, long? previousJarId = null);
        void CreateTank(string kind, Tank tank);
        void CreateTanker(string kind, Tanker tanker);
        void CreateWithdrawal(string kind, Withdrawal w);
        void CreateBottle(string kind, Bottle b);
        void CreateDispenser(string kind, Dispenser d);

        void CreateJarDetailIfNotExists(string kind, long jarId, long depositId, DateTime created);
        void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created);
        void CreateTankDetails(string kind, long tankId, IEnumerable<int> depositIds, DateTime created);
        void CreateTankerDetailIfNotExists(string kind, long tankId, long depositId, DateTime created);
        void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created);
        void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created);

        void UpdateTank(string kind, long tankId, string propertyName, string newValue);
        void UpdateTanker(string kind, long tankerId, string propertyName, string newValue);
    }

    public interface IOlapRepository : IStorageRepository { }

    public interface IStorageQueryService
    {
        Task<JarWithDeposits> DepositsInNewestJarAsync(string kind, DateTime startDate, DateTime endDate, string accountNumber = null);
        Task<JarWithDeposits> DepositsInLegacyJarAsync(string kind, long jarId, DateTime? startDate, DateTime? endDate, string accountNumber = null);
        Task<JarWithOriginData> NewestJarWithOriginAsync(string kind);
        Task<JarWithOriginData> LegacyJarWithOriginAsync(string kind, long jarId);
        Task<NextContainerForJar> NextContainerForNewestJarAsync(string kind);
        Task<NextContainerForJar> NextContainerForJarAsync(string kind, long jarId);
        Task<TankWithDeposits> TankAndAllItsDepositsAsync(string kind, long tankId);
        Task<TankWithOriginData> TankWithOriginAsync(string kind, long tankId);
        Task<NextContainerForTank> NextContainerForTankAsync(string kind, long tankId);
        Task<TankerWithDeposits> TankerAndAllItsDepositsAsync(string kind, long tankerId);
        Task<DispenserWithWithdrawals> DispenserAndAllItsWithdrawalsAsync(string kind, long dispenserId);
        Task<IEnumerable<DailyDomainSummaryData>> DailyDomainSummariesAsync(string kind, DateTime startDate, DateTime endDate, int? domainId = null);
        Task<IEnumerable<DailyDepositSummary>> DailyDepositSummariesAsync(string kind, DateTime startDate, DateTime endDate, int? domainId = null);
        Task<IEnumerable<Deposit>> DepositsByDateRangeAsync(string kind, DateTime startDate, DateTime endDate, int domainId);
        Task<IEnumerable<DailyWithdrawalSummary>> DailyWithdrawalSummariesAsync(string kind, DateTime startDate, DateTime endDate, int? domainId = null);
        Task<IEnumerable<Withdrawal>> WithdrawalsByDateRangeAsync(string kind, DateTime startDate, DateTime endDate, int domainId);
        Task<IEnumerable<string>> LastWithdrawalAddressesAsync(string kind, string accountNumber, int limit);
        Task<TankerWithMonthlyBreakdown> GetTankerMonthlyBreakdownAsync(string kind, long tankerId);
    }

    public interface IOlapQueryService : IStorageQueryService { }

    public interface IStorage
    {
        void CreateTablesIfNotExists(string kind);
        void DropAllTables(string kind);

        void CreateDeposit(string kind, Deposit depositDetails);
        void CreateJar(string kind, long version, string description, DateTime created, long? previousJarId = null);
        void CreateTank(string kind, Tank tank);
        void CreateTanker(string kind, Tanker tanker);

        void CreateWithdrawal(string kind, Withdrawal w);
        void CreateBottle(string kind, Bottle b);
        void CreateDispenser(string kind, Dispenser d);
        void CreateInvoicePayment(string kind, InvoicePayment paymentDetails);
    }

    public interface ISearchStorage : IStorage
    {
        void UpdateTank(string kind, long tankId, string propertyName, string newValue);
        void UpdateTanker(string kind, long tankerId, string propertyName, string newValue);

        Task<TransactionSearchContainer> SearchTransactionsAsync(string kind, SearchFilter filter);
        Task<IEnumerable<InvoicePayment>> InvoicePaymentsAsync(string kind, string externalAtAddress, DateTime transactionDate);
    }

    public interface IDbConnectionFactory
    {
        ClickHouseConnection CreateConnection();
        Task<IAppConnection> CreateAndOpenConnectionAsync(CancellationToken cancellationToken = default);
        IAppBulkCopy CreateAppBulkCopy(ClickHouseConnection connection);
    }

    
}
