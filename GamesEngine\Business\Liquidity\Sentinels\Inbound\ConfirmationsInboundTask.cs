﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;
using static GamesEngine.Settings.PaymentManager;

namespace GamesEngine.Business.Liquidity.Sentinels.Inbound
{
    internal class ConfirmationsInboundTask : InboundTask
    {
        internal Deposit Deposit { get; private set; }

        internal int TotalConfirmations { get; private set; }

        internal int CurrentConfirmations { get; private set; }

        internal Invoice Invoice { get; private set; }

        internal SentinelTasks SentinelInboundTasks { get; private set; }

        internal CancellationTokenSource Cancellation { get; private set; } = new();

        internal PaymentEngineDock PaymentEngineDock { get; private set; }

        internal ConfirmationsInboundTask(SentinelTasks inboundTasks, Deposit deposit, int totalConfirmations, PaymentEngineDock paymentEngineDock)
        {
            if (inboundTasks == null) throw new ArgumentNullException(nameof(inboundTasks));
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations));
            if (paymentEngineDock == null) throw new ArgumentNullException(nameof(paymentEngineDock));

            SentinelInboundTasks = inboundTasks;
            Deposit = deposit;
            TotalConfirmations = totalConfirmations;
            PaymentEngineDock = paymentEngineDock;
        }

        internal void StartInboundTask(bool itIsThePresent, Invoice invoice)
        {
            if (invoice == null) throw new ArgumentNullException(nameof(invoice));
            if (string.IsNullOrWhiteSpace(invoice.Id)) throw new ArgumentNullException(nameof(invoice.Id));
            Invoice = invoice;
            StartInboundTask(itIsThePresent);
        }

        internal override void StartInboundTask(bool itIsThePresent)
        {
            if (Cancellation.IsCancellationRequested)
            {
                SentinelInboundTasks.Detach(this);
                return;
            }

            if (Invoice == null) throw new ArgumentNullException(nameof(Invoice));
            if (task == null)
            {
                task = Task.Run(async () =>
                {
                    while (CurrentConfirmations < TotalConfirmations && !Cancellation.IsCancellationRequested)
                    {
                        await Task.Delay(SentinelTasks.DELAY_SECONDS_PER_CONFIRMATION * 1000);                        
                        try
                        {
                            CurrentConfirmations = await PaymentManager.TotalConfirmatiosAsync(Invoice);
                        }
                        catch(Exception e)
                        {
                            Loggers.GetIntance().Sentinel.Error($"Failed to get confirmations for invoice {Invoice.Id}.", e);
                        }
                    }
                    if (Cancellation.IsCancellationRequested) return;

                    await ConfirmedDepositAsync(DateTime.Now, Deposit);
                    SentinelInboundTasks.Detach(this);
                });
            }
        }

        public override string ToString()
        {
            string status = string.Empty;
            if (task == null)
            {
                status = "Not started";
            }
            else if (task.IsCompleted)
            {
                status = "Completed";
            }
            else if (task.IsCanceled)
            {
                status = "Canceled";
            }
            else if (task.IsFaulted)
            {
                status = "Faulted";
            }
            else
            {
                status = "Running";
            }
            return $"Liquidity Deposit ID {Deposit.Id} Has Risk Confirmations: {TotalConfirmations}, Current Confirmations: {CurrentConfirmations} and Task Status: {status}";
        }

        private async Task ConfirmedDepositAsync(DateTime now, Deposit draftConfirmDeposit)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit));

            if (Integration.UseKafka)
            {
                bool itIsThePresent = Puppeteer.EventSourcing.ExecutionContext.Current.ItIsThePresent;
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForLiquidityEvents))
                {
                    ConfirmedDepositDueBody msgConfirmDeposit = new ConfirmedDepositDueBody(
                        kind: draftConfirmDeposit.Kind,
                        depositId: draftConfirmDeposit.Id,
                        totalPaid: Invoice.TotalPaid,
                        rate: Invoice.Rate,
                        due: Invoice.Due
                    );
                    buffer.Send(msgConfirmDeposit);
                }
            }
        }


    }
    public class ConfirmedDepositDueBody : TypedMessage
    {
        public string Kind { get; set; }
        public int DepositId { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal Rate { get; set; }
        public decimal Due { get; set; }

        public ConfirmedDepositDueBody(string kind, int depositId, decimal totalPaid, decimal rate, decimal due) : base((char)LiquidityMessageType.SentinelConfirmDeposit)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
            if (totalPaid <= 0) throw new ArgumentNullException(nameof(totalPaid));
            if (rate <= 0) throw new ArgumentNullException(nameof(rate));
            if (due < 0) throw new ArgumentNullException(nameof(due));

            Kind = kind;
            DepositId = depositId;
            TotalPaid = totalPaid;
            Rate = rate;
            Due = due;
        }

        public ConfirmedDepositDueBody(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);

            Kind = serializedMessage[fieldOrder++];
            DepositId = int.Parse(serializedMessage[fieldOrder++]);
            TotalPaid = decimal.Parse(serializedMessage[fieldOrder++]);
            Rate = decimal.Parse(serializedMessage[fieldOrder++]);
            Due = decimal.Parse(serializedMessage[fieldOrder++]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Kind).
            AddProperty(DepositId).
            AddProperty(TotalPaid).
            AddProperty(Rate).
            AddProperty(Due);
        }
    }
}
