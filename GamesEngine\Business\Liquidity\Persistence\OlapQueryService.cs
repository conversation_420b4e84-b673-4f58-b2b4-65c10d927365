﻿using ClickHouse.Client.ADO.Parameters;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using static GamesEngine.Business.Liquidity.Persistence.OlapTableDefinitions;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class OlapQueryService : OlapServiceBase, IOlapQueryService
    {
        public OlapQueryService(IDbConnectionFactory connectionFactory) : base(connectionFactory) { }

        public async Task<JarWithDeposits> DepositsInNewestJarAsync(string kind, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            var resultData = new JarWithDeposits();

            string jarTableName = DynamicTableName(JarTableName, kind);
            string depositTableName = DynamicTableName(DepositTableName, kind);
            string assignmentTableName = DynamicTableName(DepositCurrentJarAssignmentTable, kind);

            var selectDepositsSqlBuilder = new StringBuilder($@"
                WITH NewestJarVersion AS (
                    SELECT max(Version) as MaxVersion
                    FROM {jarTableName}
                ),
                CurrentAssignmentsInNewestJar AS (
                    SELECT 
                        DepositId,
                        argMaxMerge(CurrentJarVersion_state) as CurrentVersion
                    FROM {assignmentTableName}
                    GROUP BY DepositId
                    HAVING CurrentVersion = (SELECT MaxVersion FROM NewestJarVersion)
                ),
                TotalStats AS (
                    SELECT
                        sum(Amount_final) as TotalAmount,
                        count() as DepositCount
                    FROM (
                        SELECT
                            argMaxMerge(CurrentJarVersion_state) AS EntityId_final,
                            anyMerge(Amount_state) AS Amount_final
                        FROM {assignmentTableName}
                        GROUP BY DepositId 
                    )
                    WHERE EntityId_final = (SELECT MaxVersion FROM NewestJarVersion)
                )
                SELECT
                    d.Id AS DepositId, 
                    d.DocumentNumber, 
                    d.Amount, 
                    d.Date, 
                    d.Store, 
                    d.AccountNumber, 
                    d.DomainId, 
                    d.Address, 
                    d.Created AS Created,
                    ts.TotalAmount,
                    ts.DepositCount
                FROM {depositTableName} d
                JOIN CurrentAssignmentsInNewestJar ca ON d.Id = ca.DepositId
                CROSS JOIN TotalStats ts
                WHERE d.Date >= @StartDate AND d.Date <= @EndDate
            ");

            var queryParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate, DbType = DbType.DateTime }
            };

            if (!string.IsNullOrEmpty(accountNumber))
            {
                selectDepositsSqlBuilder.Append(" AND d.AccountNumber = @AccountNumber");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = accountNumber, DbType = DbType.String });
            }
            selectDepositsSqlBuilder.Append(" ORDER BY d.Date DESC, d.Id");

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = selectDepositsSqlBuilder.ToString();
                        foreach (var p in queryParams)
                        {
                            appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                        }

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            bool totalsSet = false;
                            while (await reader.ReadAsync())
                            {
                                if (!totalsSet)
                                {
                                    resultData.TotalAmount = reader.IsDBNull("TotalAmount") ? 0m : reader.GetDecimal("TotalAmount");
                                    resultData.DepositCount = reader.IsDBNull("DepositCount") ? 0UL : Convert.ToUInt64(reader["DepositCount"]);
                                    totalsSet = true;
                                }
                                resultData.Deposits.Add(MapReaderToDeposit(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string accNumContext = string.IsNullOrEmpty(accountNumber) ? "null" : accountNumber;
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DepositsInNewestJarAsync)} deposit list (startDate: {startDate}, endDate: {endDate}, accountNumber: {accNumContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsInNewestJarAsync)} deposit list", $"startDate {startDate}, endDate {endDate}, accountNumber {accNumContext}");
            }

            return resultData;
        }

        public async Task<JarWithDeposits> DepositsInLegacyJarAsync(string kind, long jarId, DateTime? startDate, DateTime? endDate, string accountNumber = null)
        {
            var resultData = new JarWithDeposits();

            string depositTableName = DynamicTableName(DepositTableName, kind);
            string assignmentTableName = DynamicTableName(DepositCurrentJarAssignmentTable, kind);

            var selectDepositsSqlBuilder = new StringBuilder($@"
                WITH CurrentAssignmentsInJar AS (
                    SELECT 
                        DepositId
                    FROM {assignmentTableName}
                    GROUP BY DepositId
                    HAVING argMaxMerge(CurrentJarVersion_state) = @JarId
                ),
                TotalStats AS (
                    SELECT
                        sum(Amount_final) as TotalAmount,
                        count() as DepositCount
                    FROM (
                        SELECT
                            argMaxMerge(CurrentJarVersion_state) AS EntityId_final,
                            anyMerge(Amount_state) AS Amount_final
                        FROM {assignmentTableName}
                        GROUP BY DepositId 
                    )
                    WHERE EntityId_final = @JarId
                )
                SELECT
                    d.Id AS DepositId, 
                    d.DocumentNumber, 
                    d.Amount, 
                    d.Date, 
                    d.Store, 
                    d.AccountNumber, 
                    d.DomainId, 
                    d.Address, 
                    d.Created AS Created,
                    ts.TotalAmount,
                    ts.DepositCount
                FROM {depositTableName} d
                JOIN CurrentAssignmentsInJar ca ON d.Id = ca.DepositId
                CROSS JOIN TotalStats ts
            ");

            var queryParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "JarId", Value = jarId, DbType = DbType.Int64 }
            };

            var whereClauses = new List<string>();
            if (startDate.HasValue)
            {
                whereClauses.Add("d.Date >= @StartDate");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate.Value, DbType = DbType.DateTime });
            }
            if (endDate.HasValue)
            {
                whereClauses.Add("d.Date <= @EndDate");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate.Value, DbType = DbType.DateTime });
            }
            if (!string.IsNullOrEmpty(accountNumber))
            {
                whereClauses.Add("d.AccountNumber = @AccountNumber");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = accountNumber, DbType = DbType.String });
            }

            if (whereClauses.Any())
            {
                selectDepositsSqlBuilder.Append(" WHERE ").Append(string.Join(" AND ", whereClauses));
            }
            selectDepositsSqlBuilder.Append(" ORDER BY d.Date DESC, d.Id");

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = selectDepositsSqlBuilder.ToString();
                        foreach (var p in queryParams)
                        {
                            appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                        }

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            bool totalsSet = false;
                            while (await reader.ReadAsync())
                            {
                                if (!totalsSet)
                                {
                                    resultData.TotalAmount = reader.IsDBNull("TotalAmount") ? 0m : reader.GetDecimal("TotalAmount");
                                    resultData.DepositCount = reader.IsDBNull("DepositCount") ? 0UL : Convert.ToUInt64(reader["DepositCount"]);
                                    totalsSet = true;
                                }
                                resultData.Deposits.Add(MapReaderToDeposit(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string accNumContext = string.IsNullOrEmpty(accountNumber) ? "null" : accountNumber;
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DepositsInLegacyJarAsync)} deposit list (jarId: {jarId}, accountNumber: {accNumContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsInLegacyJarAsync)} deposit list", $"jarId: {jarId}, accountNumber {accNumContext}");
            }

            return resultData;
        }

        public async Task<JarWithOriginData> NewestJarWithOriginAsync(string kind)
        {
            long? newestJarId = null;
            long? originJarId = null;
            string jarTableName = DynamicTableName(JarTableName, kind);

            string getJarInfoSql = $@"
                WITH NewestJarVersion AS (
                    SELECT max(Version) as MaxVersion
                    FROM {jarTableName}
                )
                SELECT 
                    Version,
                    OriginJarId
                FROM {jarTableName}
                WHERE Version = (SELECT MaxVersion FROM NewestJarVersion)";

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = getJarInfoSql;
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                newestJarId = reader.GetInt64(reader.GetOrdinal("Version"));
                                int ordinal = reader.GetOrdinal("OriginJarId");
                                if (!reader.IsDBNull(ordinal))
                                {
                                    originJarId = reader.GetInt64(ordinal);
                                }
                            }
                        }
                    }
                }

                if (!newestJarId.HasValue)
                {
                    return null;
                }

                var currentJarData = await DepositsInLegacyJarAsync(kind, newestJarId.Value, null, null, null);

                JarWithDeposits originJarData = null;
                if (originJarId.HasValue)
                {
                    originJarData = await DepositsInLegacyJarAsync(kind, originJarId.Value, null, null, null);
                }

                return new JarWithOriginData
                {
                    CurrentJarId = newestJarId.Value,
                    CurrentJarData = currentJarData,
                    OriginJarId = originJarId,
                    OriginJarData = originJarData
                };
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(NewestJarWithOriginAsync)} (kind: {kind}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(NewestJarWithOriginAsync)}", $"kind: {kind}");
                throw;
            }
        }

        public async Task<JarWithOriginData> LegacyJarWithOriginAsync(string kind, long jarId)
        {
            long? originJarId = null;
            bool currentJarExists = false;
            string jarTableName = DynamicTableName(JarTableName, kind);
            string getJarInfoSql = $"SELECT OriginJarId FROM {jarTableName} WHERE Version = @JarId";

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = getJarInfoSql;
                        appCommand.AddParameter("JarId", jarId, DbType.Int64);
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                currentJarExists = true;
                                int ordinal = reader.GetOrdinal("OriginJarId");
                                if (!reader.IsDBNull(ordinal))
                                {
                                    originJarId = reader.GetInt64(ordinal);
                                }
                            }
                        }
                    }
                }

                if (!currentJarExists)
                {
                    var errorMessage = $"Jar with Version {jarId} does not exist in kind {kind}.";
                    var ex = new InvalidOperationException(errorMessage);
                    Loggers.GetIntance().Db.Error(errorMessage, ex);
                    ErrorsSender.Send(ex, $"{nameof(LegacyJarWithOriginAsync)}", $"JarId {jarId}");
                    throw ex;
                }

                var currentJarData = await DepositsInLegacyJarAsync(kind, jarId, null, null, null);

                JarWithDeposits originJarData = null;
                if (originJarId.HasValue)
                {
                    originJarData = await DepositsInLegacyJarAsync(kind, originJarId.Value, null, null, null);
                }

                return new JarWithOriginData
                {
                    CurrentJarData = currentJarData,
                    OriginJarData = originJarData,
                    OriginJarId = originJarId
                };
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(LegacyJarWithOriginAsync)} (JarId: {jarId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(LegacyJarWithOriginAsync)}", $"JarId {jarId}");
                throw;
            }
        }

        public async Task<NextContainerForJar> NextContainerForNewestJarAsync(string kind)
        {
            string jarTableName = DynamicTableName(JarTableName, kind);
            string getNewestJarSql = $"SELECT max(Version) FROM {jarTableName}";
            long? newestJarId = null;

            try
            {
                using (var conn = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = getNewestJarSql;
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync() && !reader.IsDBNull(0))
                            {
                                newestJarId = reader.GetInt64(0);
                            }
                        }
                    }
                }

                if (!newestJarId.HasValue)
                {
                    return null;
                }

                return await NextContainerForJarAsync(kind, newestJarId.Value);
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(NextContainerForNewestJarAsync)} (kind: {kind}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(NextContainerForNewestJarAsync)}", $"kind: {kind}");
                throw;
            }
        }

        public async Task<NextContainerForJar> NextContainerForJarAsync(string kind, long jarId)
        {
            try
            {
                string jarTableName = DynamicTableName(JarTableName, kind);
                string checkJarExistsSql = $"SELECT 1 FROM {jarTableName} WHERE Version = @JarId";
                bool jarExists = false;
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = checkJarExistsSql;
                        appCommand.AddParameter("JarId", jarId, DbType.Int64);
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            jarExists = await reader.ReadAsync();
                        }
                    }
                }

                if (!jarExists) return null;

                var currentJarData = await DepositsInLegacyJarAsync(kind, jarId, null, null, null);
                var result = new NextContainerForJar
                {
                    CurrentJarId = jarId,
                    CurrentJarData = currentJarData
                };

                string nextJarSql = $"SELECT Version FROM {DynamicTableName(JarTableName, kind)} WHERE OriginJarId = @JarId LIMIT 1";
                string nextTankSql = $"SELECT Id FROM {DynamicTableName(TankTableName, kind)} WHERE OriginId = @JarId AND OriginType = 'Jar' LIMIT 1";

                long? nextJarId = null;
                long? nextTankId = null;

                using (var conn = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = nextJarSql;
                        cmd.AddParameter("JarId", jarId, DbType.Int64);
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync()) nextJarId = reader.GetInt64(0);
                        }
                    }

                    if (!nextJarId.HasValue)
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = nextTankSql;
                            cmd.AddParameter("JarId", jarId, DbType.Int64);
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                if (await reader.ReadAsync()) nextTankId = reader.GetInt64(0);
                            }
                        }
                    }
                }

                if (nextJarId.HasValue)
                {
                    result.NextJarId = nextJarId.Value;
                    result.NextJarData = await DepositsInLegacyJarAsync(kind, nextJarId.Value, null, null, null);
                }
                else if (nextTankId.HasValue)
                {
                    result.NextTankId = nextTankId.Value;
                    result.NextTankData = await TankAndAllItsDepositsAsync(kind, nextTankId.Value);
                }

                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(NextContainerForJarAsync)} (JarId: {jarId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(NextContainerForJarAsync)}", $"JarId {jarId}");
                throw;
            }
        }

        private Deposit MapReaderToDeposit(IDataReader reader)
        {
            return new Deposit
            {
                Id = reader.GetInt64(reader.GetOrdinal("DepositId")),
                DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                Address = reader.GetString(reader.GetOrdinal("Address")),
                Created = reader.GetDateTime(reader.GetOrdinal("Created"))
            };
        }

        private Withdrawal MapReaderToWithdrawal(IDataReader reader)
        {
            return new Withdrawal
            {
                Id = reader.GetInt64(reader.GetOrdinal("WithdrawalId")),
                DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                Address = reader.GetString(reader.GetOrdinal("Address")),
                Created = reader.GetDateTime(reader.GetOrdinal("Created"))
            };
        }

        public async Task<TankWithDeposits> TankAndAllItsDepositsAsync(string kind, long tankId)
        {
            var result = new TankWithDeposits();

            string tankTableName = DynamicTableName(TankTableName, kind);
            string depositTableName = DynamicTableName(DepositTableName, kind);
            string assignmentTableName = DynamicTableName(DepositCurrentTankAssignmentTable, kind);

            string combinedSql = $@"
                WITH TankInfo AS (
                    SELECT Id, Description, Created, OriginType, OriginId 
                    FROM {tankTableName} 
                    WHERE Id = @TankId
                ),
                CurrentAssignmentsInThisTank AS (
                    SELECT 
                        DepositId
                    FROM {assignmentTableName}
                    GROUP BY DepositId
                    HAVING argMaxMerge(CurrentTankId_state) = @TankId
                ),
                TotalStats AS (
                    SELECT
                        sum(Amount_final) as TotalAmount,
                        count() as DepositCount
                    FROM (
                        SELECT
                            argMaxMerge(CurrentTankId_state) AS EntityId_final,
                            anyMerge(Amount_state) AS Amount_final
                        FROM {assignmentTableName}
                        GROUP BY DepositId 
                    )
                    WHERE EntityId_final = @TankId
                )
                SELECT
                    ti.Id as TankId,
                    ti.Description as TankDescription,
                    ti.Created as TankCreated,
                    ti.OriginType as TankOriginType,
                    ti.OriginId as TankOriginId,
                    ts.TotalAmount,
                    ts.DepositCount,
                    d.Id AS DepositId, 
                    d.DocumentNumber, 
                    d.Amount as Amount, 
                    d.Date, 
                    d.Store,
                    d.AccountNumber, 
                    d.DomainId, 
                    d.Address, 
                    d.Created AS Created
                FROM TankInfo ti
                CROSS JOIN TotalStats ts
                LEFT JOIN CurrentAssignmentsInThisTank ca ON 1=1 
                LEFT JOIN {depositTableName} d ON d.Id = ca.DepositId
                ORDER BY d.Date DESC, d.Id";

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = combinedSql;
                        appCommand.AddParameter("TankId", tankId, DbType.Int64);
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            bool tankInfoSet = false;
                            bool totalsSet = false;
                            while (await reader.ReadAsync())
                            {
                                if (!tankInfoSet)
                                {
                                    if (reader.IsDBNull("TankId"))
                                    {
                                        return null;
                                    }

                                    result.TankInfo = new Tank
                                    {
                                        Id = reader.GetInt64("TankId"),
                                        Description = reader.IsDBNull("TankDescription") ? null : reader.GetString("TankDescription"),
                                        Created = reader.GetDateTime("TankCreated"),
                                        OriginType = reader.IsDBNull("TankOriginType") ? null : reader.GetString("TankOriginType"),
                                        OriginId = reader.IsDBNull("TankOriginId") ? (long?)null : reader.GetInt64("TankOriginId")
                                    };
                                    tankInfoSet = true;
                                }

                                if (!totalsSet)
                                {
                                    result.TotalDepositsAmount = reader.IsDBNull("TotalAmount") ? 0m : reader.GetDecimal("TotalAmount");
                                    result.DepositsCount = reader.IsDBNull("DepositCount") ? 0UL : Convert.ToUInt64(reader["DepositCount"]);
                                    totalsSet = true;
                                }

                                if (!reader.IsDBNull("DepositId"))
                                {
                                    result.Deposits.Add(MapReaderToDeposit(reader));
                                }
                            }
                            if (!tankInfoSet)
                            {
                                return null;
                            }
                        }
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(TankAndAllItsDepositsAsync)} (TankId: {tankId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankAndAllItsDepositsAsync)}", $"TankId {tankId}");
                throw;
            }
        }

        public async Task<TankWithOriginData> TankWithOriginAsync(string kind, long tankId)
        {
            var tankData = await TankAndAllItsDepositsAsync(kind, tankId);

            if (tankData?.TankInfo == null || !tankData.TankInfo.OriginId.HasValue)
            {
                return null;
            }

            var result = new TankWithOriginData
            {
                CurrentTankData = tankData
            };

            var originId = tankData.TankInfo.OriginId.Value;

            if (tankData.TankInfo.OriginType == "Jar")
            {
                result.OriginJarData = await DepositsInLegacyJarAsync(kind, originId, null, null, null);
            }
            else if (tankData.TankInfo.OriginType == "Tank")
            {
                result.OriginTankData = await TankAndAllItsDepositsAsync(kind, originId);
            }
            else
            {
                return null;
            }

            if (result.OriginJarData == null && result.OriginTankData == null)
            {
                return null;
            }

            return result;
        }

        public async Task<NextContainerForTank> NextContainerForTankAsync(string kind, long tankId)
        {
            try
            {
                var currentTankData = await TankAndAllItsDepositsAsync(kind, tankId);
                if (currentTankData == null)
                {
                    return null;
                }

                var result = new NextContainerForTank { CurrentTankData = currentTankData };

                string nextTankSql = $"SELECT Id FROM {DynamicTableName(TankTableName, kind)} WHERE OriginId = @TankId AND OriginType = 'Tank' LIMIT 1";
                long? nextTankId = null;

                using (var conn = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = nextTankSql;
                        cmd.AddParameter("TankId", tankId, DbType.Int64);
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                nextTankId = reader.GetInt64(0);
                            }
                        }
                    }
                }

                if (nextTankId.HasValue)
                {
                    result.NextTankId = nextTankId.Value;
                    result.NextTankData = await TankAndAllItsDepositsAsync(kind, nextTankId.Value);
                }
                else
                {
                    string assignmentTankTable = DynamicTableName(DepositCurrentTankAssignmentTable, kind);
                    string assignmentTankerTable = DynamicTableName(DepositCurrentTankerAssignmentTable, kind);

                    string nextTankerSql = $@"
                        WITH DepositsInSourceTank AS (
                            SELECT DepositId
                            FROM {assignmentTankTable}
                            GROUP BY DepositId
                            HAVING argMaxMerge(CurrentTankId_state) = @TankId
                        )
                        SELECT TankerId FROM (
                            SELECT argMaxMerge(CurrentTankerId_state) as TankerId
                            FROM {assignmentTankerTable}
                            WHERE DepositId IN (SELECT DepositId FROM DepositsInSourceTank)
                            GROUP BY DepositId
                        )
                        WHERE TankerId IS NOT NULL
                        LIMIT 1";

                    long? nextTankerId = null;
                    using (var conn = await _connectionFactory.CreateAndOpenConnectionAsync())
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = nextTankerSql;
                            cmd.AddParameter("TankId", tankId, DbType.Int64);
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                if (await reader.ReadAsync() && !reader.IsDBNull(0))
                                {
                                    nextTankerId = reader.GetInt64(0);
                                }
                            }
                        }
                    }

                    if (nextTankerId.HasValue)
                    {
                        result.NextTankerId = nextTankerId.Value;
                        result.NextTankerData = await TankerAndAllItsDepositsAsync(kind, nextTankerId.Value);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(NextContainerForTankAsync)} (TankId: {tankId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(NextContainerForTankAsync)}", $"TankId {tankId}");
                throw;
            }
        }

        public async Task<TankerWithDeposits> TankerAndAllItsDepositsAsync(string kind, long tankerId)
        {
            var result = new TankerWithDeposits();

            string tankerTableName = DynamicTableName(TankerTableName, kind);
            string depositTableName = DynamicTableName(DepositTableName, kind);
            string assignmentTableName = DynamicTableName(DepositCurrentTankerAssignmentTable, kind);

            string combinedSql = $@"
                WITH TankerInfo AS (
                    SELECT Id, Description, Created 
                    FROM {tankerTableName} 
                    WHERE Id = @TankerId
                ),
                CurrentAssignmentsInThisTanker AS (
                    SELECT 
                        DepositId
                    FROM {assignmentTableName}
                    GROUP BY DepositId
                    HAVING argMaxMerge(CurrentTankerId_state) = @TankerId
                ),
                TotalStats AS (
                    SELECT
                        sum(Amount_final) as TotalAmount,
                        count() as DepositCount
                    FROM (
                        SELECT
                            argMaxMerge(CurrentTankerId_state) AS EntityId_final,
                            anyMerge(Amount_state) AS Amount_final
                        FROM {assignmentTableName}
                        GROUP BY DepositId 
                    )
                    WHERE EntityId_final = @TankerId
                )
                SELECT
                    ti.Id as TankerId,
                    ti.Description as TankerDescription,
                    ti.Created as TankerCreated,
                    ts.TotalAmount,
                    ts.DepositCount,
                    d.Id AS DepositId, 
                    d.DocumentNumber, 
                    d.Amount as Amount, 
                    d.Date, 
                    d.Store,
                    d.AccountNumber, 
                    d.DomainId, 
                    d.Address, 
                    d.Created AS Created
                FROM TankerInfo ti
                CROSS JOIN TotalStats ts
                LEFT JOIN CurrentAssignmentsInThisTanker ca ON 1=1 
                LEFT JOIN {depositTableName} d ON d.Id = ca.DepositId
                ORDER BY d.Date DESC, d.Id";

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = combinedSql;
                        appCommand.AddParameter("TankerId", tankerId, DbType.Int64);
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            bool tankerInfoSet = false;
                            bool totalsSet = false;

                            while (await reader.ReadAsync())
                            {
                                if (!tankerInfoSet)
                                {
                                    if (reader.IsDBNull("TankerId"))
                                    {
                                        return null;
                                    }

                                    result.TankerInfo = new Tanker
                                    {
                                        Id = reader.GetInt64("TankerId"),
                                        Description = reader.IsDBNull("TankerDescription") ? null : reader.GetString("TankerDescription"),
                                        Created = reader.GetDateTime("TankerCreated")
                                    };
                                    tankerInfoSet = true;
                                }

                                if (!totalsSet)
                                {
                                    result.TotalDepositsAmount = reader.IsDBNull("TotalAmount") ? 0m : reader.GetDecimal("TotalAmount");
                                    result.DepositsCount = reader.IsDBNull("DepositCount") ? 0UL : Convert.ToUInt64(reader["DepositCount"]);
                                    totalsSet = true;
                                }

                                if (!reader.IsDBNull("DepositId"))
                                {
                                    result.Deposits.Add(MapReaderToDeposit(reader));
                                }
                            }

                            if (!tankerInfoSet)
                            {
                                return null;
                            }
                        }
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(TankerAndAllItsDepositsAsync)} (TankerId: {tankerId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankerAndAllItsDepositsAsync)}", $"TankerId {tankerId}");
                throw;
            }
        }

        public async Task<DispenserWithWithdrawals> DispenserAndAllItsWithdrawalsAsync(string kind, long dispenserId)
        {
            var result = new DispenserWithWithdrawals();

            string dispenserTableName = DynamicTableName(DispenserTableName, kind);
            string withdrawalTableName = DynamicTableName(WithdrawalTableName, kind);
            string assignmentTableName = DynamicTableName(WithdrawalCurrentDispenserAssignmentTable, kind);

            string combinedSql = $@"
                WITH DispenserInfo AS (
                    SELECT Id, Description, Created 
                    FROM {dispenserTableName} 
                    WHERE Id = @DispenserId
                ),
                CurrentAssignmentsInThisDispenser AS (
                    SELECT 
                        WithdrawalId
                    FROM {assignmentTableName}
                    GROUP BY WithdrawalId
                    HAVING argMaxMerge(CurrentDispenserId_state) = @DispenserId
                ),
                TotalStats AS (
                    SELECT
                        sum(Amount_final) as TotalAmount,
                        count() as WithdrawalCount
                    FROM (
                        SELECT
                            argMaxMerge(CurrentDispenserId_state) AS EntityId_final,
                            anyMerge(Amount_state) AS Amount_final
                        FROM {assignmentTableName}
                        GROUP BY WithdrawalId 
                    )
                    WHERE EntityId_final = @DispenserId
                )
                SELECT
                    di.Id as DispenserId,
                    di.Description as DispenserDescription,
                    di.Created as DispenserCreated,
                    ts.TotalAmount,
                    ts.WithdrawalCount,
                    w.Id AS WithdrawalId, 
                    w.DocumentNumber, 
                    w.Amount as Amount, 
                    w.Date, 
                    w.Store,
                    w.AccountNumber, 
                    w.DomainId, 
                    w.Address, 
                    w.Created AS Created
                FROM DispenserInfo di
                CROSS JOIN TotalStats ts
                LEFT JOIN CurrentAssignmentsInThisDispenser ca ON 1=1 
                LEFT JOIN {withdrawalTableName} w ON w.Id = ca.WithdrawalId
                ORDER BY w.Date DESC, w.Id";

            try
            {

                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = combinedSql;
                        appCommand.AddParameter("DispenserId", dispenserId, DbType.Int64);
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            bool dispenserInfoSet = false;
                            bool totalsSet = false;

                            while (await reader.ReadAsync())
                            {
                                if (!dispenserInfoSet)
                                {
                                    if (reader.IsDBNull("DispenserId"))
                                    {
                                        return null;
                                    }

                                    result.DispenserInfo = new Dispenser
                                    {
                                        Id = reader.GetInt64("DispenserId"),
                                        Description = reader.IsDBNull("DispenserDescription") ? null : reader.GetString("DispenserDescription"),
                                        Created = reader.GetDateTime("DispenserCreated")
                                    };
                                    dispenserInfoSet = true;
                                }

                                if (!totalsSet)
                                {
                                    result.TotalWithdrawalsAmount = reader.IsDBNull("TotalAmount") ? 0m : reader.GetDecimal("TotalAmount");
                                    result.WithdrawalsCount = reader.IsDBNull("WithdrawalCount") ? 0UL : Convert.ToUInt64(reader["WithdrawalCount"]);
                                    totalsSet = true;
                                }

                                if (!reader.IsDBNull("WithdrawalId"))
                                {
                                    result.Withdrawals.Add(MapReaderToWithdrawal(reader));
                                }
                            }

                            if (!dispenserInfoSet)
                            {
                                return null;
                            }
                        }
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DispenserAndAllItsWithdrawalsAsync)} (DispenserId: {dispenserId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DispenserAndAllItsWithdrawalsAsync)}", $"DispenserId {dispenserId}");
                throw;
            }
        }

        public async Task<IEnumerable<DailyDomainSummaryData>> DailyDomainSummariesAsync(string kind, DateTime startDate, DateTime endDate, int? domainId = null)
        {
            string dailySummaryTableName = DynamicTableName(DailyTransactionSummaryTable, kind);

            var sqlBuilder = new StringBuilder();
            sqlBuilder.Append($@"
                SELECT
                    TransactionDate,
                    DomainId,
                    sumMerge(TotalDeposits_state) AS TotalDepositsAmount,
                    sumMerge(TotalWithdrawals_state) AS TotalWithdrawalsAmount,
                    countMerge(DepositsCount_state) AS DepositsCount,
                    countMerge(WithdrawalsCount_state) AS WithdrawalsCount
                FROM {dailySummaryTableName}
                WHERE TransactionDate >= @StartDate AND TransactionDate <= @EndDate");

            var queryParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate.Date, DbType = DbType.Date },
                new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate.Date, DbType = DbType.Date }
            };

            if (domainId.HasValue)
            {
                sqlBuilder.Append(" AND DomainId = @DomainId");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "DomainId", Value = domainId.Value, DbType = DbType.Int32 });
            }

            sqlBuilder.Append(@"
                GROUP BY TransactionDate, DomainId
                ORDER BY TransactionDate, DomainId;");

            var results = new List<DailyDomainSummaryData>();
            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = sqlBuilder.ToString();
                        if (queryParams != null)
                        {
                            foreach (var p in queryParams)
                            {
                                appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                            }
                        }

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(new DailyDomainSummaryData
                                {
                                    TransactionDate = reader.GetDateTime(reader.GetOrdinal("TransactionDate")),
                                    DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                    TotalDepositsAmount = reader.GetDecimal(reader.GetOrdinal("TotalDepositsAmount")),
                                    TotalWithdrawalsAmount = reader.GetDecimal(reader.GetOrdinal("TotalWithdrawalsAmount")),
                                    DepositsCount = Convert.ToUInt64(reader["DepositsCount"]),
                                    WithdrawalsCount = Convert.ToUInt64(reader["WithdrawalsCount"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string domainContext = domainId.HasValue ? domainId.Value.ToString() : "all";
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DailyDomainSummariesAsync)} (MV approach) (startDate: {startDate}, endDate: {endDate}, domainId: {domainContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DailyDomainSummariesAsync)} (MV approach)", $"startDate {startDate}, endDate {endDate}, domainId {domainContext}");
                throw;
            }
            return results;
        }

        public async Task<IEnumerable<DailyDepositSummary>> DailyDepositSummariesAsync(string kind, DateTime startDate, DateTime endDate, int? domainId = null)
        {
            string depositTableName = DynamicTableName(DepositTableName, kind);

            var sqlBuilder = new StringBuilder();
            sqlBuilder.Append($@"
        SELECT
            toDate(Date) AS TransactionDate,
            DomainId,
            count() AS TransactionCount,
            sum(Amount) AS TotalAmount,
            sum(CAST(Amount AS Decimal(38, 8)) * Rate) AS TotalAmountAtOriginalRate
        FROM {depositTableName}
        WHERE Date >= @StartDate AND Date <= @EndDate");

            var queryParams = new List<ClickHouseDbParameter>
    {
        new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate, DbType = DbType.DateTime },
        new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate, DbType = DbType.DateTime }
    };

            if (domainId.HasValue)
            {
                sqlBuilder.Append(" AND DomainId = @DomainId");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "DomainId", Value = domainId.Value, DbType = DbType.Int32 });
            }

            sqlBuilder.Append(@"
        GROUP BY TransactionDate, DomainId
        ORDER BY TransactionDate DESC, DomainId;");

            var results = new List<DailyDepositSummary>();
            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = sqlBuilder.ToString();
                        foreach (var p in queryParams)
                        {
                            appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                        }

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(new DailyDepositSummary
                                {
                                    TransactionDate = reader.GetDateTime(reader.GetOrdinal("TransactionDate")),
                                    DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                    TransactionCount = Convert.ToUInt64(reader["TransactionCount"]),
                                    TotalAmount = reader.GetDecimal(reader.GetOrdinal("TotalAmount")),
                                    TotalAmountAtOriginalRate = reader.GetDecimal(reader.GetOrdinal("TotalAmountAtOriginalRate"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string domainContext = domainId.HasValue ? domainId.Value.ToString() : "all";
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DailyDepositSummariesAsync)} (startDate: {startDate}, endDate: {endDate}, domainId: {domainContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DailyDepositSummariesAsync)}", $"startDate {startDate}, endDate {endDate}, domainId {domainContext}");
                throw;
            }
            return results;
        }

        public async Task<IEnumerable<Deposit>> DepositsByDateRangeAsync(string kind, DateTime startDate, DateTime endDate, int domainId)
        {
            string depositTableName = DynamicTableName(DepositTableName, kind);

            var sqlBuilder = new StringBuilder($@"
        SELECT
            Id AS DepositId,
            DocumentNumber,
            Amount,
            Date,
            Store,
            AccountNumber,
            DomainId,
            Address,
            Created
        FROM {depositTableName}
        WHERE Date >= @StartDate AND Date <= @EndDate");

            var queryParams = new List<ClickHouseDbParameter>
    {
        new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate, DbType = DbType.DateTime },
        new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate, DbType = DbType.DateTime }
    };

            sqlBuilder.Append(" AND DomainId = @DomainId");
            queryParams.Add(new ClickHouseDbParameter { ParameterName = "DomainId", Value = domainId, DbType = DbType.Int32 });
            

            sqlBuilder.Append(@"
        ORDER BY Date DESC, DomainId;");

            var results = new List<Deposit>();
            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = sqlBuilder.ToString();
                        foreach (var p in queryParams)
                        {
                            appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                        }

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(MapReaderToDeposit(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DepositsByDateRangeAsync)} (startDate: {startDate}, endDate: {endDate}, domainId: {domainId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsByDateRangeAsync)}", $"startDate {startDate}, endDate {endDate}, domainId {domainId}");
                throw;
            }
            return results;
        }

        public async Task<IEnumerable<DailyWithdrawalSummary>> DailyWithdrawalSummariesAsync(string kind, DateTime startDate, DateTime endDate, int? domainId = null)
        {
            string withdrawalTableName = DynamicTableName(WithdrawalTableName, kind);

            var sqlBuilder = new StringBuilder();
            sqlBuilder.Append($@"
        SELECT
            toDate(Date) AS TransactionDate,
            DomainId,
            count() AS TransactionCount,
            sum(Amount) AS TotalAmount,
            sum(CAST(Amount AS Decimal(38, 8)) * Rate) AS TotalAmountAtOriginalRate
        FROM {withdrawalTableName}
        WHERE Date >= @StartDate AND Date <= @EndDate");

            var queryParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate, DbType = DbType.DateTime }
            };

            if (domainId.HasValue)
            {
                sqlBuilder.Append(" AND DomainId = @DomainId");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "DomainId", Value = domainId.Value, DbType = DbType.Int32 });
            }

            sqlBuilder.Append(@"
        GROUP BY TransactionDate, DomainId
        ORDER BY TransactionDate DESC, DomainId;");

            var results = new List<DailyWithdrawalSummary>();
            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = sqlBuilder.ToString();
                        foreach (var p in queryParams)
                        {
                            appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                        }

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(new DailyWithdrawalSummary
                                {
                                    TransactionDate = reader.GetDateTime(reader.GetOrdinal("TransactionDate")),
                                    DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                    TransactionCount = Convert.ToUInt64(reader["TransactionCount"]),
                                    TotalAmount = reader.GetDecimal(reader.GetOrdinal("TotalAmount")),
                                    TotalAmountAtOriginalRate = reader.GetDecimal(reader.GetOrdinal("TotalAmountAtOriginalRate"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string domainContext = domainId.HasValue ? domainId.Value.ToString() : "all";
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(DailyWithdrawalSummariesAsync)} (startDate: {startDate}, endDate: {endDate}, domainId: {domainContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DailyWithdrawalSummariesAsync)}", $"startDate {startDate}, endDate {endDate}, domainId {domainContext}");
                throw;
            }
            return results;
        }

        public async Task<IEnumerable<Withdrawal>> WithdrawalsByDateRangeAsync(string kind, DateTime startDate, DateTime endDate, int domainId)
        {
            string withdrawalTableName = DynamicTableName(WithdrawalTableName, kind);

            var sql = $@"
                SELECT Id AS WithdrawalId, DocumentNumber, Amount, Date, Store, AccountNumber, DomainId, Address, Created
                FROM {withdrawalTableName}
                WHERE Date >= @StartDate AND Date <= @EndDate AND DomainId = @DomainId
                ORDER BY Date DESC;";

            var results = new List<Withdrawal>();
            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                using (var appCommand = appConnection.CreateCommand())
                {
                    appCommand.CommandText = sql;
                    appCommand.AddParameter("StartDate", startDate, DbType.DateTime);
                    appCommand.AddParameter("EndDate", endDate, DbType.DateTime);
                    appCommand.AddParameter("DomainId", domainId, DbType.Int32);

                    using var reader = await appCommand.ExecuteReaderAsync();
                    while (await reader.ReadAsync()) results.Add(MapReaderToWithdrawal(reader));
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(WithdrawalsByDateRangeAsync)} (startDate: {startDate}, endDate: {endDate}, domainId: {domainId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(WithdrawalsByDateRangeAsync)}", $"startDate {startDate}, endDate {endDate}, domainId {domainId}");
                throw;
            }
            return results;
        }

        public async Task<IEnumerable<string>> LastWithdrawalAddressesAsync(string kind, string accountNumber, int limit)
        {
            var withdrawalTableName = DynamicTableName(WithdrawalTableName, kind);
            var sql = $@"
                WITH LastWithdrawalPerAddress AS (
                    SELECT
                        Address,
                        max(Date) AS LastDate
                    FROM {withdrawalTableName}
                    WHERE AccountNumber = @AccountNumber
                    GROUP BY Address
                )
                SELECT Address
                FROM LastWithdrawalPerAddress
                ORDER BY LastDate DESC
                LIMIT @Limit";

            var results = new List<string>();
            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = sql;
                        appCommand.AddParameter("AccountNumber", accountNumber, DbType.String);
                        appCommand.AddParameter("Limit", limit, DbType.Int32);

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                results.Add(reader.GetString(reader.GetOrdinal("Address")));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(LastWithdrawalAddressesAsync)} (accountNumber: {accountNumber}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(LastWithdrawalAddressesAsync)}", $"accountNumber: {accountNumber}");
                throw;
            }
            return results;
        }

        public async Task<TankerWithMonthlyBreakdown> GetTankerMonthlyBreakdownAsync(string kind, long tankerId)
        {
            var result = new TankerWithMonthlyBreakdown();

            string tankerTableName = DynamicTableName(TankerTableName, kind);
            string depositTableName = DynamicTableName(DepositTableName, kind);
            string tankTableName = DynamicTableName(TankTableName, kind);
            string assignmentTableName = DynamicTableName(DepositCurrentTankerAssignmentTable, kind);
            string tankDetailTableName = DynamicTableName(TankDetailTableName, kind);

            try
            {
                using (var appConnection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    string tankerInfoSql = $"SELECT Id, Description, Created FROM {tankerTableName} WHERE Id = @TankerId";
                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = tankerInfoSql;
                        appCommand.AddParameter("TankerId", tankerId, DbType.Int64);
                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                result.TankerInfo = new Tanker
                                {
                                    Id = reader.GetInt64("Id"),
                                    Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                    Created = reader.GetDateTime("Created")
                                };
                            }
                            else
                            {
                                return null;
                            }
                        }
                    }

                    var sqlBuilder = new StringBuilder($@"
                        WITH 
                        DepositsInTanker AS (
                            SELECT 
                                DepositId
                            FROM {assignmentTableName}
                            GROUP BY DepositId
                            HAVING argMaxMerge(CurrentTankerId_state) = @TankerId
                        ),
                        DepositSourceTank AS (
                            SELECT
                                dit.DepositId,
                                if(count(td.TankId) > 0, argMax(td.TankId, td.Created), CAST(NULL, 'Nullable(Int64)')) as SourceTankId
                            FROM DepositsInTanker dit
                            LEFT JOIN {tankDetailTableName} td ON dit.DepositId = td.DepositId
                            GROUP BY dit.DepositId
                        )
                        SELECT 
                            d.Id as DepositId,
                            d.Amount as DepositAmount,
                            d.Date as DepositDate,
                            d.DocumentNumber as DepositDocumentNumber,
                            dst.SourceTankId,
                            t.Description as TankDescription,
                            t.Created as TankCreatedAt
                        FROM {depositTableName} as d
                        JOIN DepositSourceTank dst ON d.Id = dst.DepositId
                        LEFT JOIN {tankTableName} t ON dst.SourceTankId = t.Id
                    ");

                    using (var appCommand = appConnection.CreateCommand())
                    {
                        appCommand.CommandText = sqlBuilder.ToString();
                        appCommand.AddParameter("TankerId", tankerId, DbType.Int64);

                        using (var reader = await appCommand.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var detail = new TankerDepositDetail
                                {
                                    DepositId = reader.GetInt64("DepositId"),
                                    DepositAmount = reader.GetDecimal("DepositAmount"),
                                    DepositDate = reader.GetDateTime("DepositDate"),
                                    DepositDocumentNumber = reader.GetString("DepositDocumentNumber"),
                                    SourceTankId = reader.IsDBNull("SourceTankId") ? (long?)null : reader.GetInt64("SourceTankId"),
                                    TankDescription = reader.IsDBNull("TankDescription") ? null : reader.GetString("TankDescription"),
                                    TankCreatedAt = reader.IsDBNull("TankCreatedAt") ? (DateTime?)null : reader.GetDateTime("TankCreatedAt")
                                };
                                result.Deposits.Add(detail);
                                result.TotalAmount += detail.DepositAmount;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in {nameof(GetTankerMonthlyBreakdownAsync)} (TankerId: {tankerId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(GetTankerMonthlyBreakdownAsync)}", $"TankerId {tankerId}");
                throw;
            }
            return result;
        }
    }
}
