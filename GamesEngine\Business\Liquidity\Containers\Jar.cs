﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using Deposit = GamesEngine.Business.Liquidity.Transactions.Deposit;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal class Jar : Container
    {
        private readonly Dictionary<int, EnclosureDeposit> wholeEnclosureDeposits = new();

        internal Jar(Source source, string kind,Liquid liquid, int vesion) : base(vesion, kind, liquid)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));
            Source = source;
            this.version = vesion;
        }

        internal Jar(Source source, string kind, int vesion, LegacyJar legacyJar, IEnumerable<EnclosureDeposit> deposits) : base(vesion, kind, source.Liquid) {

            if (source == null) throw new ArgumentNullException(nameof(source));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (vesion <= 0) throw new ArgumentException("Version cannot be less than or equal to 0.", nameof(vesion));
            if(deposits == null) throw new ArgumentException("Deposits cannot be null or empty.", nameof(deposits));

            Source = source;
            this.version = vesion;
            PreviousLegacyJar = legacyJar;
            if (deposits.Count() != 0)
            {
                foreach (EnclosureDeposit enclosureDeposit in deposits)
                {
                    if (enclosureDeposit == null) throw new GameEngineException("The deposit is null.");
                    if (enclosureDeposit.Amount < 0) throw new GameEngineException("The deposit amount is negative.");
                    if (this.wholeEnclosureDeposits.ContainsKey(enclosureDeposit.Id)) throw new GameEngineException($"The deposit with id {enclosureDeposit.Id} already exists in the jar.");
                    this.wholeEnclosureDeposits.Add(enclosureDeposit.Id, enclosureDeposit);
                }
                RecalculateAmount();
            }
        }

        protected override Jar Delegate()
        {
            throw new NotImplementedException();
        }

        internal LegacyJar PreviousLegacyJar { get; private set; }
        internal Source Source { get; private set; }
        internal string Address => $"JAR_{Kind}_V{Version}";
        internal decimal MinimumAmount { get; private set; }

        internal IEnumerable<Deposit> Deposits => wholeEnclosureDeposits.Values.Select(enclouse => enclouse.Deposit);
        internal IEnumerable<Deposit> DepositsDescending
        {
            get
            {
                var depositsArray = new Deposit[wholeEnclosureDeposits.Count];
                int i = 0;
                foreach (var ed in wholeEnclosureDeposits.Values)
                {
                    depositsArray[i++] = ed.Deposit;
                }
                return depositsArray.OrderByDescending(t => t.CreatedAt);
            }
        }

        internal decimal AvailableAmount
        {
            get
            {
                decimal result = 0;
                foreach (Deposit deposit in ConfirmedDeposits())
                {
                    result += deposit.Amount;
                }
                return result;
            }
        }

        internal bool AllDepositsAreConsummated => wholeEnclosureDeposits.Values.All(ed => ed.IsConfirmed || ed.IsCanceled);

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, string address, int depositId, string invoiceId, int authorizationId, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            return CreateDraftDeposit(itIsThePresent, createdAt, address, depositId, invoiceId, authorizationId, string.Empty, externalReference, destination, exchangeAmount, exchangeRate, confirmedCurrency, confirmedAmount, totalConfirmations, storeId, domain);
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, string address, int depositId, string invoiceId, int authorizationId, string externalAtAddress, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (domain == null) throw new GameEngineException("The domain is null or empty.");
            if (storeId <= 0) throw new GameEngineException("The store id is zero or negative.");

            if (depositId <= 0) throw new GameEngineException("The deposit id is zero or negative.");
            if (string.IsNullOrWhiteSpace(destination)) throw new GameEngineException("The invoice id is null or empty.");
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt date is invalid or empty.");

            foreach (Deposit deposit in Deposits)
            {
                if (deposit.Address == address) throw new GameEngineException($"The address: {address} is already associated with an existing deposit.");
            }

            if (string.IsNullOrWhiteSpace(invoiceId)) throw new GameEngineException("The invoice id is null or empty.");
            if (string.IsNullOrWhiteSpace(destination)) throw new GameEngineException("The destination is null or empty.");
            if (exchangeAmount <= 0) throw new GameEngineException("The exchange amount is zero or negative.");
            if (exchangeRate <= 0) throw new GameEngineException("The rate is zero or negative.");

            if (authorizationId <= 0) throw new GameEngineException("The authorization id is zero or negative.");
            if (externalReference <= 0) throw new GameEngineException("The external reference is zero or negative.");

            if (string.IsNullOrWhiteSpace(confirmedCurrency)) throw new GameEngineException("The currency is null or empty.");
            if (confirmedAmount <= 0) throw new GameEngineException("The amount is zero or negative.");

            if (totalConfirmations < 0) throw new GameEngineException("The total confirmations cannot be negative.");

            if (wholeEnclosureDeposits.ContainsKey(depositId)) throw new GameEngineException($"Deposit Id {depositId} already exists in the jar.");

            var hasPaymentDock = Source.Liquid.ParentFlow.ExistPaymentEngineDock(domain);
            if (!hasPaymentDock) throw new GameEngineException($"No payment engine dock found for domain {domain}.");
            var paymentDock = Source.Liquid.ParentFlow.FindPaymentEngineDock(domain);

            Deposit depositResult = new Deposit(
                depositId,
                address,
                createdAt, 
                Kind, 
                invoiceId, 
                authorizationId,
                externalAtAddress,
                externalReference,
                destination,
                exchangeAmount,
                exchangeRate, 
                confirmedCurrency, 
                confirmedAmount,
                storeId,
                domain
            );
            
            EnclosureDeposit enclosureDeposit = new EnclosureDeposit(depositResult, Version);
            Source.DepositConsecutive = depositResult.Id;
            wholeEnclosureDeposits.Add(depositId, enclosureDeposit);
            RecalculateAmount();

            Source.Liquid.IngressSentinel.AwaitForDeposit(enclosureDeposit, totalConfirmations, paymentDock);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    DraftDepositMessage deposit = new DraftDepositMessage(
                        depositResult.Id,
                        Kind,
                        destination,
                        authorizationId.ToString(),
                        confirmedAmount,
                        createdAt,
                        Version,
                        externalAtAddress,
                        depositResult.Domain.Id,
                        exchangeRate
                    );
                    buffer.Send(deposit);
                }
            }
            
            DraftDepositEvent draftDepositConfirmedEvent = new DraftDepositEvent(createdAt, depositResult.Id, depositResult.Amount);
            PlatformMonitor.GetInstance().WhenNewEvent(draftDepositConfirmedEvent);

            return depositResult;
        }

        internal Deposit ConfirmDeposit(bool itIsThePresent, DateTime confirmDate, Deposit deposit)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (deposit == null) throw new GameEngineException("Draft confirm deposit is null.");
            if (confirmDate == DateTime.MinValue) throw new GameEngineException("The createdAt date is invalid or empty.");

            if (string.IsNullOrEmpty(deposit.InvoiceId)) throw new GameEngineException("The deposit transaction id is null or empty.");
            if (deposit.Amount <= 0) throw new GameEngineException("The deposit amount is zero or negative.");

            if (!wholeEnclosureDeposits.ContainsKey(deposit.Id)) throw new GameEngineException("Deposit Id does not exist.");

            bool hasPaymentEngineDock = Source.Liquid.ParentFlow.ExistPaymentEngineDock(deposit.Domain);
            if (!hasPaymentEngineDock) throw new GameEngineException($"No payment engine dock found for domain {deposit.Domain}.");

            if (!wholeEnclosureDeposits.TryGetValue(deposit.Id, out EnclosureDeposit enclouseDeposit))
            {
                throw new GameEngineException($"The enclosure deposit with id {deposit.Id} does not exist.");
            }

            if (enclouseDeposit.IsConfirmed) throw new GameEngineException("The deposit is already confirmed.");
            enclouseDeposit.Confirm(confirmDate);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    ConfirmedDepositMessage confirmedDepositMessage = new ConfirmedDepositMessage(
                        enclouseDeposit.Id,
                        confirmDate
                    );
                    buffer.Send(confirmedDepositMessage);
                }
            }

            return deposit;
        }

        internal Deposit CancelDeposit(bool itIsThePresent, DateTime createdAt, Deposit depositToCancel)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (depositToCancel == null) throw new ArgumentNullException(nameof(depositToCancel));
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt));
            if (!wholeEnclosureDeposits.ContainsKey(depositToCancel.Id)) throw new InvalidOperationException("Deposit Id does not exist.");
            
            if (!wholeEnclosureDeposits.TryGetValue(depositToCancel.Id, out EnclosureDeposit enclosureDeposit))
            {
                throw new GameEngineException($"The enclosure deposit with id {depositToCancel.Id} does not exist.");
            }
            enclosureDeposit.Cancel(createdAt);

            RecalculateAmount();
            Source.Liquid.IngressSentinel.StopAndDetachInboundTask(depositToCancel);

            return depositToCancel;
        }

        internal Tank FundTank(decimal amountForTank)
        {
            throw new NotImplementedException();
            if (amountForTank < MinimumAmount) throw new GameEngineException($"The request amount {amountForTank} for the tank is lower than the minimum amount {MinimumAmount}");
            //Rubicon: Todo aqui se deben extraer los depositos de cercanos al monto para crear y devolver un Tank
        }

        internal Deposit FindDepositById(int id)
        {
            if (id <= 0) throw new GameEngineException("The id is invalid or empty.");
            return wholeEnclosureDeposits[id].Deposit;
        }

        internal EnclosureDeposit FindEnclosureDeposit(int id)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));

            if (!wholeEnclosureDeposits.TryGetValue(id, out EnclosureDeposit enclosureDeposit))
            {
                throw new GameEngineException($"The enclosure deposit with id {id} does not exist.");
            }
            return enclosureDeposit;
        }

        internal IEnumerable<Deposit> PendingDeposits()
        {
            List<Deposit> pendingDeposits = new List<Deposit>();
            foreach (EnclosureDeposit ed in wholeEnclosureDeposits.Values)
            {
                if (!ed.IsConfirmed)
                {
                    pendingDeposits.Add(ed.Deposit);
                }
            }
            return pendingDeposits;
        }

        internal IEnumerable<Deposit> PendingDeposits(string externalAtAddress)
        {
            if (string.IsNullOrWhiteSpace(externalAtAddress)) throw new GameEngineException("The deposit transaction id is null or empty.");

            List<Deposit> pendingDeposits = new List<Deposit>();
            foreach (EnclosureDeposit ed in wholeEnclosureDeposits.Values)
            {
                if (!ed.IsConfirmed && ed.Deposit.ExternalAtAddress == externalAtAddress)
                {
                    pendingDeposits.Add(ed.Deposit);
                }
            }
            return pendingDeposits;
        }

        internal IEnumerable<Deposit> ConfirmedDeposits()
        {
            List<Deposit> confirmedDeposits = new List<Deposit>();
            foreach (EnclosureDeposit enclosure in wholeEnclosureDeposits.Values)
            {
                if (enclosure.IsConfirmed)
                {
                    confirmedDeposits.Add(enclosure.Deposit);
                }
            }
            return confirmedDeposits;
        }


        internal decimal EstimateNextTankAmount()
        {
            throw new NotImplementedException();
            //Rubicon: Todo, revisar hasta cuando se considera un deposito como reciente. retronar
            
            //var confirmed = _deposits.Where(d => d.Status == DepositStatus.Confirmed).ToList();
            //return confirmed.Any() ? confirmed.Average(d => d.Amount) : 0;
        }


        internal TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, string description, int vesion, IEnumerable<int> selectedDeposits)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");

            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");

            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            var confirmedDepositList = ConfirmedDeposits();
            List<Deposit> selectedDepositsList = new List<Deposit>();

            foreach (int depositId in selectedDeposits)
            {
                Deposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                selectedDepositsList.Add(confirmedDeposit);
            }
            if(selectedDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");

            return CreateTank(itIsThePresent, now, id, name, description, vesion, selectedDepositsList);
        }

        internal TankReady CreateTankWithTank(bool itIsThePresent, DateTime now, int id, string name, string description, IEnumerable<int> tankIds)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (tankIds == null || !tankIds.Any()) throw new GameEngineException("No deposits selected.");
            if (tankIds.Any(tankId => tankId <= 0)) throw new GameEngineException("One or more deposits are null.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if(tankIds.Distinct().Count() != tankIds.Count()) throw new GameEngineException("One or more tank ids are duplicated.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            List<Tank> tanksToAdd = new List<Tank>();

            foreach (int tankId in tankIds)
            {
                if( !this.Source.ExistTank(tankId)) throw new GameEngineException($"The tank with id {tankId} does not exist.");
                var tank = this.Source.FindTank(tankId);
                if(tank.Id != tankId) throw new GameEngineException($"The tank with id {tankId} was not found.");
                if (!(tank is TankReady)) throw new GameEngineException($"The tank with id {tankId} is not a TankReady. It is a {tank.GetType().Name}.");
                tanksToAdd.Add(tank);
            }
            if (tanksToAdd.Count == 0) throw new GameEngineException("No deposits where found in selected.");

            TankReady tankResult = new TankReady(id, name, description, now, 1, Kind, Liquid, tanksToAdd);
            TankCreatedEvent createdTankEvent = new TankCreatedEvent(tankResult.Id);
            PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
            Source.AddOrUpdateTank(tankResult);
            Source.AddLRUTankContainer(tankResult);
            return tankResult;
        }

        internal TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, string description, int vesion, IEnumerable<Deposit> selectedDeposits)
        {
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");

            if (selectedDeposits.Any(deposit => deposit == null)) throw new GameEngineException("One or more deposits are null.");

            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            var newLegacyJar = new LegacyJar(this);

            List<EnclosureDeposit> enclosureDeposits = new List<EnclosureDeposit>();
            foreach (Deposit deposit in selectedDeposits)
            {
                if (!wholeEnclosureDeposits.ContainsKey(deposit.Id)) throw new GameEngineException($"The deposit with id {deposit.Id} does not exist in the jar.");
                enclosureDeposits.Add(wholeEnclosureDeposits[deposit.Id]);
            }
            TankReady result = new TankReady(id, name,description, now, version: 1, Kind, Liquid, enclosureDeposits);

            List<EnclosureDeposit> delegatedEnclosureDeposits = new List<EnclosureDeposit>();
            foreach (EnclosureDeposit enclosureDeposit in wholeEnclosureDeposits.Values)
            {
                bool delegateDeposit = !selectedDeposits.Any(x => x.Id == enclosureDeposit.Id);
                if (delegateDeposit)
                {
                    delegatedEnclosureDeposits.Add(enclosureDeposit);
                }
            }
            Source.DelegateJar(itIsThePresent, now, vesion, newLegacyJar, delegatedEnclosureDeposits);
            RecalculateAmount();

            TankCreatedEvent createdTankEvent = new TankCreatedEvent(result.Id);
            PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);

            Source.AddOrUpdateTank(result);
            Source.AddLRUTankContainer(result);
            return result;
        }

        internal TankReady CreateEmptyTank(bool itIsThePresent, DateTime now, int id, string name, string description, int vesion)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (Source.ExistTank(id)) throw new GameEngineException("The tank id is already associated with an existing tank.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            TankReady result = new TankReady(id, name, description, now, 1, Kind, Liquid);
            
            RecalculateAmount();

            TankCreatedEvent createdTankEvent = new TankCreatedEvent(result.Id);
            PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);

            Source.AddOrUpdateTank(result);
            Source.AddLRUTankContainer(result);
            return result;
        }


        internal void MoveToTank(bool itIsThePresent, DateTime now, int vesion, int tankId, IEnumerable<Deposit> selectedDeposits)
        {
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");

            if (selectedDeposits.Any(deposit => deposit == null)) throw new GameEngineException("One or more deposits are null.");

            if (tankId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");
            if(!Source.ExistTank(tankId)) throw new GameEngineException($"The tank with id {tankId} does not exist.");
            var tank = Source.FindTank(tankId);
            if (!(tank is TankReady tankReady)) throw new GameEngineException($"The tank with id {tankId} is not a TankReady. It is a {tank.GetType().Name}.");

            List<EnclosureDeposit> delegatedDeposits = new List<EnclosureDeposit>();
            List<EnclosureDeposit> selectedEnclosureDeposits = new List<EnclosureDeposit>();
            foreach (EnclosureDeposit enclosureDeposit in wholeEnclosureDeposits.Values)
            {
                bool delegateDeposit = !selectedDeposits.Any(x => x.Id == enclosureDeposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(enclosureDeposit);
                }
                else
                {
                    selectedEnclosureDeposits.Add(enclosureDeposit);
                }
            }

            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);
            RecalculateAmount();

            tankReady.AddDeposits(selectedEnclosureDeposits);
            Source.AddLRUTankContainer(tankReady);
            TankDepositsHaveBeenMovedEvent CreatedTankEvent = new TankDepositsHaveBeenMovedEvent(tankReady.Id);
            PlatformMonitor.GetInstance().WhenNewEvent(CreatedTankEvent);
        }

        internal void MoveToTank(bool itIsThePresent, DateTime now, int vesion, int tankId, IEnumerable<int> selectedDeposits)
        {
            if (tankId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");
            if (tankId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            var confirmedDepositList = ConfirmedDeposits();
            List<Deposit> selectedDepositsList = new List<Deposit>();

            foreach (int depositId in selectedDeposits)
            {
                Deposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                selectedDepositsList.Add(confirmedDeposit);
            }
            if (selectedDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");
            MoveToTank(itIsThePresent, now, vesion, tankId, selectedDepositsList);
        }

        internal void MoveToTanker(bool itIsThePresent, DateTime now, int vesion, int tankerId, IEnumerable<int> selectedDeposits)
        {
            if (tankerId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");
            if (tankerId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (!Source.ExistTanker(tankerId)) throw new GameEngineException($"The tanker with id {tankerId} does not exist.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");
            var tanker = Source.FindTanker(tankerId);
            if (!(tanker is TankerPending tankerPending)) throw new GameEngineException($"The tanker with id {tankerId} is not a TankerPending. It is a {tanker.GetType().Name}.");

            var confirmedDepositList = ConfirmedDeposits();
            List<EnclosureDeposit> selectedEnclosureDepositsList = new List<EnclosureDeposit>();

            foreach (int depositId in selectedDeposits)
            {
                Deposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                var confirmedEnclosureDeposit = FindEnclosureDeposit(depositId);
                selectedEnclosureDepositsList.Add(confirmedEnclosureDeposit);
            }
            if (selectedEnclosureDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");


            List<EnclosureDeposit> delegatedEnclosureDeposits = new List<EnclosureDeposit>();
            foreach (EnclosureDeposit enclosureDeposit in wholeEnclosureDeposits.Values)
            {
                bool delegateDeposit = !selectedEnclosureDepositsList.Any(x => x.Id == enclosureDeposit.Id);
                if (delegateDeposit)
                {
                    delegatedEnclosureDeposits.Add(enclosureDeposit);
                }
            }

            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedEnclosureDeposits);
            RecalculateAmount();

            tankerPending.Add(selectedEnclosureDepositsList);
        }

        protected override DateTime CalculateMinDate()
        {
            
            DateTime result = DateTime.MaxValue;
            if (Deposits != null && Deposits.Any())
            {
                result = Deposits.Min(d => d.CreatedAt);
            }
           

            return result;
        }
        protected override DateTime CalculateMaxDate()
        {
            DateTime result = DateTime.MinValue;
            if (Deposits != null && Deposits.Any())
            {
                result = Deposits.Max(d => d.CreatedAt);
            }
            return result;
        }

        public Tank OptimizedDeposit()
        {
            //Rubicon: Todo, Calcula es los mejores depósitos candidatos para ser movidos  a un Tank. Los Depósitos cumplen en monto
            throw new NotImplementedException();
        }

        public bool ExistDeposit(int depositId)
        {
            if (depositId <= 0) throw new GameEngineException("The deposit id is invalid or empty.");
            return wholeEnclosureDeposits.ContainsKey(depositId);
        }

        public bool ExistDepositConfirmed(int depositId)
        {
            if (depositId <= 0) throw new GameEngineException("The deposit id is invalid or empty.");
            if (ExistDeposit(depositId))
            {
                var enclosureDeposit = wholeEnclosureDeposits[depositId];
                return enclosureDeposit.IsConfirmed;
            }
            return false;
        }

        internal CompletedDepositSummary BuildRecentDeposits(List<Deposit> completedDeposits,DateTime now)
        {
            if (completedDeposits == null) throw new ArgumentNullException(nameof(completedDeposits));
            int totalTransactions = this.ConfirmedDeposits().Count();
            decimal totalConfirmedAmount = this.ConfirmedDeposits().Sum(deposit => deposit.ConfirmedAmount);
            decimal totalCompletedAmount = this.ConfirmedDeposits().Sum(deposit => deposit.Amount);
            if (!completedDeposits.Any())
            {
                var summary = new CompletedDepositSummary
                {
                    TotalConfirmedAmount = totalConfirmedAmount.ToString("N2", Integration.CultureInfoEnUS),
                    TotalCompletedAmount = totalCompletedAmount,
                    TotalTransactions = totalTransactions,
                    TotalLastHourConfirmed = CompletedDepositSummary.Default.TotalLastHourConfirmed,
                    TotalPerHourConfirmed = CompletedDepositSummary.Default.TotalPerHourConfirmed,
                    TotalLastHourCompletedAmount = CompletedDepositSummary.Default.TotalLastHourCompletedAmount,
                    TotalPerHourCompleted = CompletedDepositSummary.Default.TotalPerHourCompleted,
                    TotalTransactionsLastHour = CompletedDepositSummary.Default.TotalTransactionsLastHour,
                    TotalTransactionsPerHour = CompletedDepositSummary.Default.TotalTransactionsPerHour,
                    PeakHour = CompletedDepositSummary.Default.PeakHour
                };
                return summary;
            }

            var totalConfirmedAmountPerHour = completedDeposits.Sum(deposit => deposit.ConfirmedAmount);
            var totalCompletedAmountPerHour = completedDeposits.Sum(deposit => deposit.Amount);

            DateTime minCreatedAt = completedDeposits.Min(deposit => deposit.CreatedAt);
            double totalHours = (now - minCreatedAt).TotalHours;
            decimal totalPerHourConfirmed = totalHours > 0 ? totalConfirmedAmountPerHour / (decimal)totalHours : 0;
            decimal totalPerHourCompleted = totalHours > 0 ? totalCompletedAmountPerHour / (decimal)totalHours : 0;
            

            int? peakHour = completedDeposits
                .GroupBy(deposit => deposit.CreatedAt.Hour)
                .OrderByDescending(g => g.Count())
                .FirstOrDefault()?.Key;

            int totalTransactionsPerHour = completedDeposits
                .GroupBy(deposit => deposit.CreatedAt.Hour)
                .Select(g => g.Count())
                .DefaultIfEmpty(0)
                .Max();

            return new CompletedDepositSummary
            {
                TotalConfirmedAmount = totalConfirmedAmount.ToString("N2", Integration.CultureInfoEnUS),
                TotalLastHourConfirmed = totalConfirmedAmount.ToString("N2", Integration.CultureInfoEnUS), 
                TotalPerHourConfirmed = totalPerHourConfirmed.ToString("N2", Integration.CultureInfoEnUS),
                TotalCompletedAmount = totalCompletedAmount,
                TotalLastHourCompletedAmount = totalCompletedAmount, 
                TotalPerHourCompleted = totalPerHourCompleted,
                TotalTransactions = totalTransactions,
                TotalTransactionsLastHour = totalTransactions, 
                TotalTransactionsPerHour = totalTransactionsPerHour,
                PeakHour = peakHour.HasValue ? peakHour.Value : 0
            };
        }

        private IEnumerable<Deposit> OrderDepositsByCreated(List<Deposit> deposits)
        {
            if (deposits == null) throw new ArgumentNullException(nameof(deposits));
            return deposits.OrderByDescending(t => t.CreatedAt);
        }

        internal IEnumerable<Deposit> BuildDepositsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == DateTime.MinValue && endDate == DateTime.MinValue)  throw new GameEngineException("The start date and end date cannot be MinValue.");
            
            if(startDate != DateTime.MinValue && endDate != DateTime.MinValue && startDate > endDate) throw new ArgumentException("The start date cannot be greater than the end date.");

            List<Deposit> result = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                if (deposit.CreatedAt >= startDate && deposit.CreatedAt <= endDate)
                {
                    result.Add(deposit);
                }
            }

            return OrderDepositsByCreated(result);
        }

        internal IEnumerable<Deposit> BuildDepositsFrom(DateTime from)
        {
            return BuildDepositsBetween(from, DateTime.MaxValue);
        }

        internal IEnumerable<Deposit> BuildDepositsUpTo(DateTime to)
        {
            return BuildDepositsBetween(DateTime.MinValue, to);
        }

        //internal void TryReleaseVersion(int releaseVersion)
        //{
        //    //validaciones
        //    if (releaseVersion <= 0) throw new ArgumentException(nameof(releaseVersion));

        //    int latestVersion = Source.CurrentVesion;
        //    if (releaseVersion >= latestVersion) throw new GameEngineException("Cannot release the current or a future version.");

        //    //si yo soy la version hacer
        //    if (Version == releaseVersion)
        //    {
        //        bool allDepositsAreConsummated = AllDepositsAreConsummated;
        //        bool stillHasPrevious = PreviousLegacyJar != null;
        //        if (allDepositsAreConsummated)
        //        {

        //        }
        //    }
        //    //si no ir mas abajo en la cadena de jars
        //    else
        //    {
        //        PreviousLegacyJar?.TryReleaseVersion(releaseVersion);
        //    }


        //    //if (PreviousLegacyJar == null) return;
        //    //PreviousLegacyJar.TryReleaseVersion(releaseVersion);
        //    //if (Version <= releaseVersion) PreviousLegacyJar = null;
        //}

        internal int Versions
        {
            get
            {
                int count = 0;
                Jar? current = this;
                while (current != null)
                {
                    count++;
                    current = current.PreviousLegacyJar?.Jar;
                }
                return count;
            }
        }

        internal bool TryReleaseVersion(int removeVersion)
        {
            if (removeVersion <= 0) throw new ArgumentNullException(nameof(removeVersion));

            Jar? lastTarget = this;
            Jar? currentTarget = lastTarget.PreviousLegacyJar?.Jar;

            while (currentTarget != null && currentTarget.Id != removeVersion)
            {
                lastTarget = currentTarget;
                currentTarget = lastTarget.PreviousLegacyJar?.Jar;
            }

            if (currentTarget == null) return false;

            if (currentTarget.AllDepositsAreConsummated)
            {
                lastTarget.PreviousLegacyJar = currentTarget.PreviousLegacyJar;
                return true;
            }

            return false;
        }

        internal bool ExistVersion(int version)
        {
            if (version <= 0) throw new ArgumentNullException(nameof(version));
            Jar? jar = this;
            while (jar != null)
            {
                if (jar.Id == version) return true;
                jar = jar.PreviousLegacyJar?.Jar;
            }
            return false;
        }

        internal Jar FindJar(int version)
        {
            if (version <= 0) throw new ArgumentNullException(nameof(version));
            Jar? jar = this;
            while (jar != null)
            {
                if (jar.Id == version) return jar;
                jar = jar.PreviousLegacyJar?.Jar;
            }
            throw new GameEngineException($"The jar with version {version} does not exist.");
        }


        internal TankerPending CreateTankerWithDeposits(bool itIsThePresent,DateTime createdAt,int version,int tankerId, string name, string description, IEnumerable<int> deposits)
        {
            List<EnclosureDeposit> enclosureDeposits = new List<EnclosureDeposit>();
            foreach (int depositId in deposits)
            {
                if(depositId <=0) throw new ArgumentNullException(nameof(depositId));
                if (!ExistDeposit(depositId)) throw new GameEngineException($"The deposit with id {depositId} does not exist in the jar.");
                var enclosureDeposit = wholeEnclosureDeposits[depositId];
                if(!enclosureDeposit.IsConfirmed) throw new GameEngineException($"The deposit with id {depositId} is not confirmed.");
                enclosureDeposits.Add(enclosureDeposit);
            }
            var newLegacyJar = new LegacyJar(this);
            List<EnclosureDeposit> delegatedEnclosureDeposits = new List<EnclosureDeposit>();
            foreach (EnclosureDeposit enclosureDeposit in wholeEnclosureDeposits.Values)
            {
                bool delegateDeposit = !enclosureDeposits.Any(x => x.Id == enclosureDeposit.Id);
                if (delegateDeposit)
                {
                    delegatedEnclosureDeposits.Add(enclosureDeposit);
                }
            }
            
            Source.DelegateJar(itIsThePresent, createdAt, version, newLegacyJar, delegatedEnclosureDeposits);
            RecalculateAmount();
            var tanker = new TankerPending(tankerId, name, description, createdAt, 1, Kind, this.Source, enclosureDeposits);
            Source.AddOrUpdateTanker(tanker);

            CreatedTankerEvent createdTankEvent = new CreatedTankerEvent(tankerId);
            PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
            return tanker;
        }

        public class CompletedDepositSummary : Objeto
        {
            public static CompletedDepositSummary Default => new CompletedDepositSummary
            {
                TotalConfirmedAmount = "0.00",
                TotalLastHourConfirmed = "0.00",
                TotalPerHourConfirmed = "0.00",
                TotalCompletedAmount = 0,
                TotalLastHourCompletedAmount = 0,
                TotalPerHourCompleted = 0,
                TotalTransactions = 0,
                TotalTransactionsLastHour = 0,
                TotalTransactionsPerHour = 0,
                PeakHour = 0
            };

            public string TotalConfirmedAmount { get; set; }
            public string TotalLastHourConfirmed { get; set; }
            public string TotalPerHourConfirmed { get; set; }
            public decimal TotalCompletedAmount { get; set; }
            public decimal TotalPerHourCompleted { get; set; }
            public decimal TotalLastHourCompletedAmount { get; set; }
            public int TotalTransactions { get; set; }
            public int TotalTransactionsLastHour { get; set; }
            public int TotalTransactionsPerHour { get; set; }
            public int PeakHour { get; set; }
        }

    }
}
