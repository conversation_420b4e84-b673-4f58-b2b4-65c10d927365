﻿using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;


namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal class SentinelTasks
    {
        internal static int DELAY_SECONDS_PER_CONFIRMATION = 3;

        private readonly ConcurrentDictionary<object, SentinelTask> boundTasks = new();

        internal SentinelTasks()
        {
        }

        internal IEnumerable<SentinelTask> Tasks => boundTasks.Values;

        internal SentinelTask FindTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            if (boundTasks.TryGetValue(objectKey, out var inboundTask))
            {
                return inboundTask;
            }
            throw new GameEngineException($"No inbound task found for invoice ID: {objectKey}.");
        }

        internal void AddTask(object taskObjectKey, SentinelTask task)
        {
            if (taskObjectKey == null) throw new ArgumentNullException(nameof(taskObjectKey));
            if (task == null) throw new ArgumentNullException(nameof(task));

            if (!boundTasks.TryAdd(taskObjectKey, task))
            {
                throw new GameEngineException($"Task with key {taskObjectKey} already exists.");
            }
        }

        internal ConfirmationsInboundTask AddConfirmationsWatcher(Deposit deposit, int totalConfirmations, PaymentEngineDock engineDock)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations));
            if (engineDock == null) throw new ArgumentNullException(nameof(engineDock));

            if (!boundTasks.TryGetValue(deposit.InvoiceId, out SentinelTask confirmsInboundTask))
            {
                confirmsInboundTask = new ConfirmationsInboundTask(this, deposit, totalConfirmations, engineDock);
                boundTasks.TryAdd(deposit.InvoiceId, confirmsInboundTask);
            }
            return confirmsInboundTask as ConfirmationsInboundTask;
        }

        internal void Detach(SentinelTask inoutboundTask)
        {
            if (inoutboundTask is ConfirmationsInboundTask confirmationsInbound)
            {
                boundTasks.TryRemove(confirmationsInbound.Deposit, out _);
            }
            else if (inoutboundTask is TankerInboundTask tankerInbound)
            {
                boundTasks.TryRemove(tankerInbound.Tanker, out _);
            }
            else if (inoutboundTask is ConfimationsOutboundTask confimationsOutbound)
            {
                boundTasks.TryRemove(confimationsOutbound, out _);
            }
            else
            {
                throw new GameEngineException("Invalid inbound task type. Only ConfirmationsInboundTask can be detached.");
            }
        }

        internal TankerInboundTask AwaitForTanker(TankerSealed tanker)
        {
            if (tanker == null) throw new ArgumentNullException(nameof(tanker));

            if (boundTasks.TryGetValue(tanker, out SentinelTask existingTask))
            {
                return existingTask as TankerInboundTask;
            }

            var newTankerInboundTask = new TankerInboundTask(tanker);
            boundTasks.TryAdd(tanker, newTankerInboundTask);
            return newTankerInboundTask;
        }

        internal ConfimationsOutboundTask AwaitWithdrawalConfirmations(DispenserReady dispenserReady, PaymentEngineDock paymentEngineDock)
        {
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
            if (paymentEngineDock == null) throw new ArgumentNullException(nameof(paymentEngineDock));

            var confimationsOutboundTask = new ConfimationsOutboundTask(this, dispenserReady, paymentEngineDock);
            boundTasks.TryAdd(dispenserReady, confimationsOutboundTask);
            return confimationsOutboundTask;
        }
    }
}
