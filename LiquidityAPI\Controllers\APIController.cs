using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System.Runtime.Serialization;
using System.Text;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Business.WholePaymentProcessor.PaymentProcessor;
using static GamesEngine.Exchange.town.connectors.drivers.fierro.processors.UnlockDeposit;
using static GamesEngine.Settings.PaymentManager;
using static GamesEngine.Settings.PaymentManager.Invoice;
using static LiquidityAPI.Controllers.ConfirmationsByRangesController;
using static town.connectors.CustomSettings;

namespace LiquidityAPI.Controllers
{
    public class APIController : AuthorizeController
    {
        private readonly ILogger<APIController> _logger;

        public APIController(ILogger<APIController> logger)
        {
            _logger = logger;
        }

        [HttpGet("api/liquidity/{kind}")]
        public async Task<IActionResult> AllDepositsAsync(string kind, DateTime startDate, DateTime endDate)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");

            StringBuilder cmdFilterDeposit = new StringBuilder();
            if (startDate != DateTime.MinValue && endDate != DateTime.MinValue)
            {
                if (startDate > endDate)
                    return BadRequest("startDate must be less than or equal to endDate");
                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterDeposit.AppendLine($"allDeposits = source.Jar.BuildDepositsBetween({formattedStartDate}, {formattedEndDate});");
            }
            else if (startDate != DateTime.MinValue && endDate == DateTime.MinValue)
            {
              string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
              cmdFilterDeposit.AppendLine($"allDeposits = source.Jar.BuildDepositsFrom({formattedStartDate});");
            }
            else if (startDate == DateTime.MinValue && endDate != DateTime.MinValue)
            {
               string endFormattedDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterDeposit.AppendLine($"allDeposits = source.Jar.BuildDepositsUpTo({endFormattedDate});");
            }
            else
            {
                cmdFilterDeposit.AppendLine("allDeposits = source.Jar.DepositsDescending;");
            }

            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                print source.Jar.MinDate minDate;
                print source.Jar.MaxDate maxDate;
                {cmdFilterDeposit}
                for (deposits : allDeposits)
                {{
                    deposit = deposits;
                    print deposit.Id id;
                    print deposit.Amount amount;
                    print deposit.ConfirmedAmount receivedAmount;
                    print deposit.Address address;
                    print deposit.Kind kind;
                    print deposit.CreatedAt createdAt;
                    print deposit.InvoiceId invoiceId;
                    print deposit.Destination destination;
                    print deposit.Rate rate;
                    print deposit.Domain.Id domainId;
                    print deposit.Domain.Url domainUrl;
                    print deposit.ExternalAtAddress externalAtAddress;
                    print deposit.ValueDirectionAsString({resultRate.Value}) valueDirection;

                    enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit(deposit.Id);
                    print enclosureDeposit.ConfirmedDate confirmedDate;
                    print enclosureDeposit.CanceledDated canceledDate;

                }}
                
                
            }}";

            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, script);

            return result;
        }

        [HttpGet("api/liquidity/{kind}/awaiting")]
        public async Task<IActionResult> AwaitingDepositsAsync(string kind, [FromQuery] string atAddress)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest("atAddress is required");
            string atAddressScaped = Validator.StringEscape(atAddress);
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                awaitingDeposits = source.Jar.PendingDeposits('{atAddressScaped}');
                for (deposits : awaitingDeposits)
                {{
                    deposit = deposits;
                    print deposit.Id id;
                    print deposit.Amount amount;
                    print deposit.ConfirmedCurrency confirmedCurrency;
                    print deposit.ConfirmedAmount confirmedAmount;
                    print deposit.Address address;
                    print deposit.CreatedAt createdAt;
                    print deposit.InvoiceId invoiceId;
                    print deposit.Rate rate;
                    print deposit.Kind kind;
                    print deposit.ValueDirectionAsString({resultRate.Value}) valueDirection;
                }}
                
            }}";

            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, script);

            return result;
        }

        [HttpGet("api/liquidity/{kind}/tanks")]
        public async Task<IActionResult> AllTanksFromKindAsync(string kind,DateTime startDate,DateTime endDate, string status, int fromTankId = 0)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (fromTankId < 0)
            {
                return BadRequest("fromTankId must be greater or equal to zero");
            }
            FilterTankStatus newTankStatus;
            if (string.IsNullOrEmpty(status))
            {
                newTankStatus = FilterTankStatus.ALL;
            }
            else if (!Enum.TryParse<FilterTankStatus>(status, true, out newTankStatus))
            {
                return BadRequest("Invalid tankStatus value.");
            }
            StringBuilder cmdFilterTank = new StringBuilder();
            StringBuilder qryFilterTank = new StringBuilder();


            if (startDate != DateTime.MinValue && endDate != DateTime.MinValue)
            {
                if (startDate > endDate)
                    return BadRequest("startDate must be less than or equal to endDate");

                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

                cmdFilterTank.AppendLine($"allTanks = liquid.Source.BuildTankWithDepositsBetween({formattedStartDate}, {formattedEndDate}, {newTankStatus});");
            }
            else if (startDate != DateTime.MinValue)
            {
                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterTank.AppendLine($"allTanks = liquid.Source.BuildTankWithDepositsFrom({formattedStartDate}, {newTankStatus});");
            }
            else if (endDate != DateTime.MinValue)
            {
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterTank.AppendLine($"allTanks = liquid.Source.BuildTankWithDepositsUpTo({formattedEndDate}, {newTankStatus});");
            }
            else if (fromTankId == 0)
            {
                cmdFilterTank.AppendLine($"allTanks = liquid.Source.AvailableTanks({newTankStatus});");
            }
            else if (fromTankId > 0)
            {
                qryFilterTank.AppendLine($@"
                    if(existKind)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        existTank = liquid.Source.ExistTank({fromTankId});
                        Check(existTank) Error 'The request tank id:{fromTankId} does not exist.';
                    }}
                ");
                cmdFilterTank.AppendLine($@"tank = liquid.Source.FindTank({fromTankId});");
                cmdFilterTank.AppendLine("allTanks = tank.DescendantTanks(true);");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                {qryFilterTank}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                {cmdFilterTank}
                print liquid.Source.MaxDateTank maxDate;
                print liquid.Source.MinDateTank minDate;

                for (tanks : allTanks)
                {{
                    tank = tanks;
                    print tank.Id id;
                    print tank.Name name;
                    print tank.Description description;
                    //print tank.Version version;
                    print tank.Type status;
                    print tank.Amount totalAmount;
                    print tank.ReceivedAmount receivedAmount;
                    print tank.CreatedAt createdAt;
                    print tank.ContainerColor color;
                    print tank.TargetDate targetDate;
                    print tank.GoalAmount.GetAmount() goalAmount;
                    print tank.Path path;
                    print tank.DescendentChildrenId() childrenIds;
                    print tank.IsInsideTanker() isInsideTanker;
                    for (parentPaths : tank.ParentChain)
                    {{
                        path = parentPaths;
                        print path.Id id;
                        print path.Name name;
                    }}  
                }}

                for (obsoleteTanks : liquid.Source.ObsoleteTanks)
                {{
                    tank = obsoleteTanks;
                    print tank.Id id;
                    print tank.Name name;
                    print tank.Description description;
                    print tank.Type status;
                    print tank.Amount totalAmount;
                    print tank.CreatedAt createdAt;
                    print tank.ContainerColor color;
                    print tank.TargetDate targetDate;
                    print tank.GoalAmount.GetAmount() goalAmount;
                    print tank.Path path;
                    for (parentPaths : tank.ParentChain)
                    {{
                        path = parentPaths;
                        print path.Id id;
                        print path.Name name;
                    }}  
                }}
            }}
            ");
            return result;
        }

        //HACER OTRO SERVICIO PARA DETALLES DE TANKS PERO GENERICO NO TAN ESPECIFICO

        [HttpGet("api/liquidity/{kind}/tanks/{tankId}")]
        public async Task<IActionResult> DetailsTankAsync(string kind, int tankId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required");
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTank = liquid.Source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                }}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                print tank.Id id;
                print tank.Name name;
                print tank.Description description;
                print tank.Amount totalAmount;
                print tank.ContainerColor color;
                print tank.GoalAmount.GetAmount() goalAmount;
                print tank.CreatedAt createdAt;
                print tank.TargetDate targetDate;
                print tank.ReceivedAmount receivedAmount;
                print tank.Path path;
                for (parentPaths : tank.ParentChain)
                {{
                    path = parentPaths;
                    print path.Id id;
                    print path.Name name;
                }}  
                print tank.ExchangeVariation({resultRate.Value}) exchangeRateVariation;
                for (deposits : tank.Deposits)
                {{
                    deposit = deposits;
                    print deposit.Id id;
                    print deposit.CreatedAt createdAt;
                    print deposit.ExternalAtAddress externalAtAddress;
                    print deposit.Domain.Url domainUrl;
                    print deposit.Amount amount;
                    print deposit.ExchangeVariation({resultRate.Value}) exchangeRateVariation;
                    print deposit.ValueDirectionAsString({resultRate.Value}) valueDirection;
                }}

                for (flattenedTanks : tank.FlattenedList)
                {{
                    tank = flattenedTanks;
                    print tank.Id id;
                    print tank.Name name;
                    print tank.Description description;
                    print tank.Amount amount;
                    print tank.ReceivedAmount receivedAmount;
                    print tank.ContainerColor color;
                    print tank.Path path;
                }}
            }}
            ");
            return result;
        }


        [HttpPut("api/liquidity/{kind}/tank/{tankId}/update/color")]
        public async Task<IActionResult> ChangeColorTankAsync(string kind, int tankId, [FromBody] BodydColor body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("Body is required");
            if (body.NewColor == null) return BadRequest("Body NewColor is required");
            

            string scapedColor = !string.IsNullOrEmpty(body.NewColor) ? Validator.StringEscape(body.NewColor) : "";

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    existTank = source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    if(existTank)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        tank = liquid.Source.FindTank({tankId});
                        isSameColor = tank.ItsSameColor('{scapedColor}');
                        Check(!isSameColor) Error 'The new color must be different from the old color.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                tank.ChangeColor('{scapedColor}');
            }}");

            return result;
        }


        [HttpGet("api/liquidity/{kind}/tanker")]
        public async Task<IActionResult> AllTankerFromKindAsync(string kind, DateTime startDate, DateTime endDate, string status)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
           
            TankerStatus newTankerStatus;
            if (string.IsNullOrEmpty(status))
            {
                newTankerStatus = TankerStatus.ALL;
            }
            else if (!Enum.TryParse<TankerStatus>(status, true, out newTankerStatus))
            {
                return BadRequest("Invalid tankerStatus value.");
            }

            StringBuilder cmdFilterTankers = new StringBuilder();
            if (startDate != DateTime.MinValue && endDate != DateTime.MinValue)
            {
                if (startDate > endDate)
                    return BadRequest("startDate must be less than or equal to endDate");

                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

                cmdFilterTankers.AppendLine($"allTankers = liquid.Source.BuildTankerWithDepositsBetween({formattedStartDate}, {formattedEndDate},{newTankerStatus});");
            }
            else if (startDate != DateTime.MinValue)
            {
                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterTankers.AppendLine($"allTankers = liquid.Source.BuildTankerWithDepositsFrom({formattedStartDate},{newTankerStatus});");
            }
            else if (endDate != DateTime.MinValue)
            {
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterTankers.AppendLine($"allTankers = liquid.Source.BuildTankerWithDepositsUpTo({formattedEndDate},{newTankerStatus});");
            }
            else
            {
                cmdFilterTankers.AppendLine($"allTankers = liquid.Source.FilterTankersByStatus({newTankerStatus});");
            }

            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                print liquid.Source.MaxDateTanker maxDate;
                print liquid.Source.MinDateTanker minDate;
                {cmdFilterTankers}
                for (tankers : allTankers)
                {{
                    tanker = tankers;
                    print tanker.Id id;
                    print tanker.Name name;
                    print tanker.Description description;
                    print tanker.ContainerColor color;
                    print tanker.Type status;
                    print tanker.CreatedAt createdAt;
                    print tanker.Amount totalAmount;
                    print tanker.ReceivedAmount receivedAmount;
                    print tanker.TotalTanks totalTanks;
                    print tanker.GoalAmount.GetAmount() goalAmount;
                    print tanker.TargetDate targetDate;
                }}

                for (obsoleteTankers : liquid.Source.ObsoleteTankers)
                {{
                    tanker = obsoleteTankers;
                    print tanker.Id id;
                    print tanker.Name name;
                    print tanker.Description description;
                    print tanker.ContainerColor color;
                    print tanker.Type status;
                    print tanker.CreatedAt createdAt;
                    print tanker.Amount totalAmount;
                    print tanker.TotalTanks totalTanks;
                    print tanker.GoalAmount.GetAmount() goalAmount;
                    print tanker.TargetDate targetDate;
                }}
            }}
            ");
            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/update/color")]
        public async Task<IActionResult> ChangeColorTankerAsync(string kind, int tankerId, [FromBody] BodydColor body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("Body is required");
            if (body.NewColor == null) return BadRequest("Body NewColor is required");

            string scapedColor = !string.IsNullOrEmpty(body.NewColor) ? Validator.StringEscape(body.NewColor) : "";
            StringBuilder cmdChangeColor = new StringBuilder();
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    existTanker = source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if(existTanker)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        source = liquid.Source;
                        tanker = source.FindTanker({tankerId});
                        isSameColor = tanker.ItsSameColor('{scapedColor}');
                        Check(!isSameColor) Error 'The new color must be different from the old color.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                tanker = source.FindTanker({tankerId});
                tanker.ChangeColor('{scapedColor}');
            }}");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/tanker/{tankerId}")]
        public async Task<IActionResult> AllTanksFromTankerKindAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    for (sources : liquid.Sources)
                    {{
                        source = sources;
                        existTanker = source.ExistTanker({tankerId});
                        Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    }}
                }}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;
                    
                    tanker = source.FindTanker({tankerId});
                    for (tanks : tanker.Tanks)
                    {{
                        tank = tanks;
                        print tank.Id id;
                        print tank.Name name;
                        print tank.Type status;
                        print tank.ContainerColor color;
                    }}
                   
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/liquidity/{kind}/tankers/{tankerId}/depositsAndAddresses")]
        public async Task<IActionResult> TankerDepositsAndAddressesAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");

            var jsonResult = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    existTanker = source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                }}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                tanker = source.FindTanker({tankerId});

                print tanker.Id id;
                print tanker.Name name;
                print tanker.Description description;

                for (tanks : tanker.Tanks)
                {{
                    for (deposits : tanks.ExplandedDeposits)
                    {{
                        deposit = deposits;
                        print deposit.Amount amount;
                        print deposit.Destination address;
                    }}
                }}
            }}
            ");

            if (!(jsonResult is OkObjectResult okResult))
            {
                return jsonResult;
            }

            var jsonString = okResult.Value.ToString();
            try
            {
                var responseData = JsonConvert.DeserializeObject<CsvTankerData>(jsonString);
                return GenerateCsvFile(tankerId, responseData);
            }
            catch (JsonException ex)
            {
                return BadRequest("Internal error processing csv data.");
            }
        }

        private IActionResult GenerateCsvFile(int tankerId, CsvTankerData? responseData)
        {
            if (responseData == null || responseData.Tanks == null || responseData.Tanks.Count == 0) return BadRequest("Failed to parse tanks response.");

            var csvBuilder = new StringBuilder();
            var tankerDescription = string.IsNullOrEmpty(responseData.Description) ? responseData.Name : SanitizeCsvField(responseData.Description);
            if (string.IsNullOrEmpty(tankerDescription)) tankerDescription = "No Description";
            foreach (var tank in responseData.Tanks)
            {
                if (tank.Deposits == null || tank.Deposits.Count == 0) return BadRequest("Failed to parse deposits response.");
                foreach (var deposit in tank.Deposits)
                {
                    if (string.IsNullOrWhiteSpace(deposit.Address)) return BadRequest("Address is missing in one of the deposits.");
                    if (deposit.Amount <= 0) return BadRequest("Amount must be greater than zero in all deposits.");
                    csvBuilder.AppendLine($"{deposit.Address},{deposit.Amount},{tankerDescription}");
                }
            }

            var csvBytes = Encoding.UTF8.GetBytes(csvBuilder.ToString());
            var fileName = $"tanker-{tankerId}-deposits.csv";

            return File(csvBytes, "text/csv", fileName);
        }

        private string SanitizeCsvField(string fieldData)
        {
            if (string.IsNullOrEmpty(fieldData)) return string.Empty;

            if (fieldData.IndexOf(',') != -1 || fieldData.IndexOf('"') != -1)
            {
                var sanitizedField = fieldData.Replace("\"", "\"\"");
                return $"\"{sanitizedField}\"";
            }

            return fieldData;
        }

        [HttpGet("api/liquidity/{kind}/tanker/{tankerId}/summary")]
        public async Task<IActionResult> TankerSummaryAsync(string kind, int tankerId, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate, [FromQuery] string name, [FromQuery] string type, [FromQuery] string color)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required");
            if (startDate.HasValue && endDate.HasValue && startDate > endDate)
            {
                return BadRequest("startDate must be less than or equal to endDate");
            }
            if (startDate.HasValue && startDate.Value.TimeOfDay != TimeSpan.Zero)
            {
                return BadRequest("The startDate parameter must not include a time component (hours, minutes, or seconds). Please provide the date only (e.g., 'MM/dd/yyyy').");
            }

            if (endDate.HasValue && endDate.Value.TimeOfDay != TimeSpan.Zero)
            {
                return BadRequest("The endDate parameter must not include a time component (hours, minutes, or seconds). Please provide the date only (e.g., 'MM/dd/yyyy').");
            }

            var escapedName = string.IsNullOrWhiteSpace(name) ? string.Empty : Validator.StringEscape(name);
            FilterContainerType containerType = string.IsNullOrWhiteSpace(type)
                ? FilterContainerType.ALL
                : Enum.TryParse<FilterContainerType>(type, true, out var parsedStatus) ? parsedStatus : FilterContainerType.ALL;
            var escapedColor = string.IsNullOrWhiteSpace(color) ? string.Empty : Validator.StringEscape(color);

            DateTime effectiveStartDate = startDate ?? new DateTime(1951, 1, 1);
            DateTime effectiveEndDate = endDate ?? new DateTime(2049, 12, 31);

            string formattedStartDate = effectiveStartDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
            string formattedEndDate = effectiveEndDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    existTanker = source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                }}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
        
                tanker = source.FindTanker({tankerId});

                print tanker.Id tankerId;
                print tanker.Name tankerName;
                print tanker.Description description;

                summary = tanker.BuildMonthlySummary({formattedStartDate}, {formattedEndDate}, '{escapedName}', {containerType}, '{escapedColor}');
                print summary.TankerTotalAmount totalAmount;
                print summary.TotalReceivedAmount totalReceivedAmount;
                print summary.TotalTanks totalTanks;
                print summary.TotalDeposits totalDeposits;
                
                for (monthData : summary.MonthlySummaries)
                {{
                    print monthData.Month month;
                    print monthData.MonthTotal monthTotal;

                    for (tanks : monthData.Tanks)
                    {{
                        tank = tanks;
                        print tank.Id id;
                        print tank.Name name;
                        print tank.Type status;
                        print tank.Amount amount;
                        print tank.CreatedAt createdAt;
                    }}
                    for (rootDeposits : monthData.RootDeposits)
                    {{
                        deposit = rootDeposits;
                        print deposit.Id id;
                        print deposit.InvoiceId invoiceId;
                        print deposit.Amount amount;
                        print deposit.CreatedAt createdAt;
                    }}
                }}
            }}
            ");
            return result;
        }


        [HttpGet("api/liquidity/{kind}/tank/{tankId}/summary")]
        public async Task<IActionResult> TankSummaryAsync(string kind, int tankId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required");

            DateTime effectiveStartDate =  new DateTime(1951, 1, 1);
            DateTime effectiveEndDate =  new DateTime(2049, 12, 31);

            string formattedStartDate = effectiveStartDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
            string formattedEndDate = effectiveEndDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    existTank = source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    if(existTank)
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        isRootTank = tank{tankId}.Type == '{typeof(TankRoot).Name}';
                        isAnValidTank = isTankReady || isRootTank;
                        Check(isAnValidTank) Error 'Tank id: {tankId} is not support type and cannot retrieve information.';
                    }}
                }}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                tank = source.FindTank({tankId});

                print tank.Id tankId;
                print tank.Name tankName;
                print tank.Description description;
                print tank.ContainerColor color;

                summary = tank.BuildMonthlySummary({formattedStartDate}, {formattedEndDate}, '', '', '');
                print summary.TankTotalAmount totalAmount;
                print summary.TotalReceivedAmount totalReceivedAmount;
                print summary.TotalTanks totalTanks;
                print summary.TotalDeposits totalDeposits;
                for (monthData : summary.MonthlySummaries)
                {{
                    print monthData.Month month;
                    print monthData.MonthTotal monthTotal;

                    for (deposits : monthData.Deposits)
                    {{
                        deposit = deposits;
                        print deposit.Id id;
                        print deposit.InvoiceId invoiceId;
                        print deposit.Amount amount;
                        print deposit.ReceivedAmount receivedAmount;
                        print deposit.CreatedAt createdAt;
                    }}
                      
                    for (tanks : monthData.Tanks)
                    {{
                        tank = tanks;
                        print tank.Id id;
                        print tank.Name name;
                        print tank.Type status;
                        print tank.Amount amount;
                        print tank.ReceivedAmount receivedAmount;
                        print tank.CreatedAt createdAt;
                    }}
                    
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/liquidity/{kind}/dispenser/{dispenserId}/summary")]
        public async Task<IActionResult> DispenserSummaryAsync(string kind, int dispenserId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("DispenserId is required");
            DateTime effectiveStartDate = new DateTime(1951, 1, 1);
            DateTime effectiveEndDate = new DateTime(2049, 12, 31);

            string formattedStartDate = effectiveStartDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
            string formattedEndDate = effectiveEndDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The request dispenser id:{dispenserId} does not exist.';
                    if(existDispenser)
                    {{
                        dispenser{dispenserId} = liquid.Outlet.FindDispenser({dispenserId});
                        isDispenserReady = dispenser{dispenserId}.Type == '{typeof(DispenserReady).Name}';
                        isDispenserInbox = dispenser{dispenserId}.Type == '{typeof(DispenserInbox).Name}';
                        isAnValidDispenser = isDispenserReady || isDispenserInbox;
                        Check(isAnValidDispenser) Error 'Dispenser id: {dispenserId} is not support type and cannot retrieve information.';
                    }}
                }}
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});

                print dispenser.Id dispenserId;
                print dispenser.Name dispenserName;
                print dispenser.Description description;
                print dispenser.ContainerColor color;

                summary = dispenser.BuildMonthlySummary({formattedStartDate}, {formattedEndDate}, '', '', '');
                print summary.DispenserTotalAmount totalAmount;
                print summary.TotalReceivedAmount totalReceivedAmount;
                print summary.TotalDispensers totalDispensers;
                print summary.TotalWithdrawals totalWithdrawals;
                for (monthData : summary.MonthlySummaries)
                {{
                    print monthData.Month month;
                    print monthData.MonthTotal monthTotal;

                    for (withdrawals : monthData.Withdrawals)
                    {{
                        withdrawal = withdrawals;
                        print withdrawal.Id id;
                        print withdrawal.Amount amount;
                        print withdrawal.ReceivedAmount receivedAmount;
                        print withdrawal.CreatedAt createdAt;
                    }}

                    for (flattenedDispensers : monthData.DispenserDetail)
                    {{
                        dispenser = flattenedDispensers;
                        print dispenser.Id id;
                        print dispenser.Name name;
                        print dispenser.Amount amount;
                    }}
                    }}
                
            }}
            ");
            return result;
        }

        public class RetrieveDepositData
        {
            public int Id { get; set; }
            public string InvoiceId { get; set; }
            public int AuthorizationId { get; set; }
            public decimal Amount { get; set; }
            public decimal Rate { get; set; }
            public decimal ConfirmedAmount { get; set; }
            public string ConfirmedCurrency { get; set; }
            public string Address { get; set; }
            public string DepositAddress { get; set; }
            public int StoreId { get; set; }
            public int AgentId { get; set; }
            public int DomainId { get; set; }
            public string DomainUrl { get; set; }
            public int ExternalReference { get; set; }
            public string ExternalAtAddress { get; set; }
            public string Destination { get; set; }
            public string PaymentLink { get; set; }
            public bool IsPending { get; set; }
            public bool IsConfirmed { get; set; }
            public bool IsCanceled { get; set; }
            public decimal ExchangeRateVariation { get; set; }
            public string ValueDirection { get; set; }

           
        }

        public class RetrieveTime
        {
            public string Now { get;set; }
        }

        public class ResponseDepositDetail
        {
            public int Id { get; set; }
            public string InvoiceId { get; set; }
            public decimal Amount { get; set; }
            public decimal Rate { get; set; }
            public decimal ConfirmedAmount { get; set; }
            public string ConfirmedCurrency { get; set; }
            public int ExternalReference { get; set; }
            public string ExternalAtAddress { get; set; }
            public string Destination { get; set; }
            public decimal ReceivedAmount { get; set; }
            public decimal IntendedAmount { get; set; }
            public decimal ExchangeRateVariation { get; set; }
            public string ValueDirection { get; set; }
        }

        public class NextWithdrawDTO
        {
            public int WithdrawId { get; set; }
            public string DomainUrl { get; set; }
            public int DomainId { get; set; }
            public int StoreId { get; set; }
            public int AgentId { get; set; }
            public string PaymentDockId { get; set; }
            public decimal ExchangeAmount { get; set; }
        }

        private async Task<IActionResult> RetrieveNextWithdrawAsync(string kind, string domain, decimal rate,decimal receivedAmount)
        {
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest("domain-url is required");
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if(rate <= 0) return BadRequest("rate must be greater than zero");

            var withdrawIdQuery = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existDomain = company.Sales.ExistsDomain('{domain}');
                Check(existDomain) Error 'The domain:{domain} does not exist.';
                if (existDomain)
                {{
                    existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                    Check(existKind) Error 'The request kind:{kind} does not exist.';

                    if (existKind)
                    {{
                        domain = company.Sales.DomainFrom('{domain}');
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                        Check(existPaymentDock) Error 'The payment engine dock for the domain:{domain} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                Eval('withdrawId = '+liquid.Outlet.NextWithdrawalId()+';');
                print withdrawId withdrawId;

                domain = company.Sales.DomainFrom('{domain}');
                print domain.Id domainId;
                print domain.Url domainUrl;
                print domain.AgentId agentId;
                print liquid.Outlet.CalculateExchangeAmount({rate},{receivedAmount}) exchangeAmount;
                print company.Sales.CurrentStore.Id storeId;
                
                paymentDock = liquid.ParentFlow.FindPaymentEngineDock(domain);
                print paymentDock.Dock paymentDockId;
            }}
            ");
            return withdrawIdQuery;
        }

        public class DepositDTO
        {
            public int DepositId { get; set; }
        }

        public class NextDepositDTO : DepositDTO
        {
            public string DomainUrl { get; set; }
            public int DomainId { get; set; }
            public int StoreId { get; set; }
            public int AgentId { get; set; }
            public string PaymentDockId { get; set; }
        }

        private async Task<IActionResult> RetrieveNextDepositAsync(string kind, string domain)
        {
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest("domain-url is required");
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            var depositIdQuery = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existDomain = company.Sales.ExistsDomain('{domain}');
                Check(existDomain) Error 'The domain:{domain} does not exist.';
                if (existDomain)
                {{
                    existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                    Check(existKind) Error 'The request kind:{kind} does not exist.';

                    if (existKind)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        domain = company.Sales.DomainFrom('{domain}');
                        existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                        Check(existPaymentDock) Error 'The payment engine dock for the domain:{domain} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                Eval('depositId = '+liquid.Source.NextDepositId()+';');
                print depositId depositId;

                domain = company.Sales.DomainFrom('{domain}');
                print domain.Id domainId;
                print domain.Url domainUrl;
                print domain.AgentId agentId;
                
                print company.Sales.CurrentStore.Id storeId;

                paymentDock = liquid.ParentFlow.FindPaymentEngineDock(domain);
                print paymentDock.Dock paymentDockId;
            }}");
            return depositIdQuery;
        }
        private async Task<IActionResult> RetriveDepositDataAsync(string kind, int depositId)
        {
            return await RetriveDepositDataAsync(kind, depositId, 1M);
        }
        private async Task<IActionResult> RetriveDepositDataAsync(string kind, int depositId, decimal rate)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("kind is required", nameof(kind));
            if (depositId <= 0) throw new ArgumentException("depositId must be greater than zero", nameof(depositId));
            var depositQuery = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                    existDeposit = liquid.Source.Jar.ExistDeposit({depositId});
                    Check(existDeposit) Error 'The deposit:{depositId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                deposit = liquid.Source.Jar.FindDepositById({depositId});

                print liquid.Source.Jar.Address address;

                print deposit.Id id;
                print deposit.InvoiceId invoiceId;
                print deposit.AuthorizationId authorizationId;
                print deposit.Amount amount;
                print deposit.Rate rate;
                print deposit.ConfirmedAmount confirmedAmount;
                print deposit.ConfirmedCurrency confirmedCurrency;
                print deposit.Address depositAddress;
                print deposit.StoreId storeId;
                print deposit.Domain.Id domainId;
                print deposit.Domain.Url domainUrl;
                print deposit.Domain.AgentId agentId;
                print deposit.ExternalReference externalReference;
                print deposit.ExternalAtAddress externalAtAddress;
                print deposit.Destination destination;
                print deposit.PaymentLink paymentLink;
                print deposit.ExchangeVariation({rate}) exchangeRateVariation;
                print deposit.ValueDirectionAsString({rate}) valueDirection;
                
                enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit(deposit.Id);  
                print enclosureDeposit.IsPending isPending;
                print enclosureDeposit.IsConfirmed isConfirmed;
                print enclosureDeposit.IsCanceled isCanceled;
            }}
            ");
            return depositQuery;
        }

        [HttpPost("api/liquidity/{kind}/deposit/draftconfirm")]
        public async Task<IActionResult> DraftDepositAsync(string kind, [FromBody] DraftDeposit body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest("domain-url is required");
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (body.ConfirmedAmount <= 0) return BadRequest("Amount must be greater than zero.");
            if (string.IsNullOrEmpty(body.ConfirmedCurrency)) return BadRequest("Currency is required.");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest("AtAddress is required.");
            if (string.IsNullOrWhiteSpace(body.EmployeeName)) return BadRequest("EmployeeName is required.");
            if (body.ExternalReference <= 0) return BadRequest("ExternalReference must be greater than zero.");

            var depositIdQuery = await RetrieveNextDepositAsync(kind, domain);
            if (!(depositIdQuery is OkObjectResult)) return depositIdQuery;

            OkObjectResult o = (OkObjectResult)depositIdQuery;
            string json = o.Value.ToString();
            var nexDepositData = JsonConvert.DeserializeObject<NextDepositDTO>(json);
            if (nexDepositData == null) return BadRequest("Failed to retrieve deposit ID.");

            int invoceReference = nexDepositData.DepositId;
            var invoiceInfo = await PaymentManager.CreateInvoiceAsync(
                body.ConfirmedCurrency,
                body.ConfirmedAmount,
                invoceReference,
                string.Empty
            );

            if (invoiceInfo == null) return BadRequest($"Failed to create invoice for kind: {kind} and amount: {body.ConfirmedAmount}.");
            if (string.IsNullOrWhiteSpace(invoiceInfo.Id)) return BadRequest("The invoice id is required.");

            var invoicePayment = await PaymentManager.InvoicePaymentMethodAsync(invoiceInfo.Id);
            if (invoicePayment == null) return BadRequest($"Failed to retrieve payment methods for invoice {invoiceInfo.Id}.");
            if (string.IsNullOrWhiteSpace(invoicePayment.Destination)) return BadRequest($"The invoice {invoiceInfo.Id} does not have a destination link.");
            if (string.IsNullOrWhiteSpace(invoicePayment.Rate)) return BadRequest($"The invoice {invoiceInfo.Id} does not have a rate.");
            if (!decimal.TryParse(invoicePayment.Rate, out decimal exchangeRate)) return BadRequest($"The invoice {invoiceInfo.Id} does not have a valid rate.");
            if (string.IsNullOrWhiteSpace(invoicePayment.Amount)) return BadRequest($"The invoice {invoiceInfo.Id} does not have a valid amount.");
            if (!decimal.TryParse(invoicePayment.Amount, out decimal exchangeAmount)) return BadRequest($"The invoice {invoiceInfo.Id} does not have a valid amount.");

            int DaysToBecomeUseless = 7; //Rubicon: El deposito se vuelve inutilizable a los 7 dias de ser creado.
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);

            var domainUrlEscaped = Validator.StringEscape(domain);
            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{domainUrlEscaped}');
                riskProfile = riskProfiles.GetRiskProfile(domain);
                confirmations = riskProfile.ConfirmationsForAmount({body.ConfirmedAmount});
                Print confirmations 'confirmations';
            }}");
            if (!(result is OkObjectResult)) return result;
            //Rubicon: parece ser que el riskProfile no tiene forma de setearle la moneda, solo permite USD. TODO cambiarlo task
            OkObjectResult okResult = (OkObjectResult)result;
            string jsonResult = okResult.Value.ToString();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var riskConfirmations = System.Text.Json.JsonSerializer.Deserialize<RiskConfirmationsDTO>(jsonResult, options);
            if (riskConfirmations == null) return BadRequest("Failed to retrieve risk confirmations.");

            int depositAuthorization = await CreditDepositAndLockAsync(kind, nexDepositData, body.AtAddress, body.EmployeeName, exchangeAmount, exchangeRate);
            if (depositAuthorization == ASITenantDriver.FAKE_TICKET_NUMBER || depositAuthorization == 0) return BadRequest("Could not get a valid authorization ID for the deposit.");

            var depositCommand = await CreateDraftDepositAsync(
                kind,
                domain,
                body.AtAddress,
                nexDepositData.DepositId,
                invoiceInfo.Id,
                depositAuthorization,
                body.ExternalReference,
                invoicePayment.Destination,
                invoicePayment.PaymentLink,
                exchangeAmount,
                exchangeRate,
                body.ConfirmedCurrency,
                body.ConfirmedAmount,
                riskConfirmations.Confirmations,
                nexDepositData.StoreId
            );
            return depositCommand;
        }

        private async Task<IActionResult> CreateDraftDepositAsync(string kind, string domain, string atAddress, int depositId, string invoiceId, int depositAuthorization, int externalReference, string invoiceDestination, string invoicePaymentLink, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId)
        {
            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existsDomain = company.Sales.ExistsDomain('{domain}');
                Check(existsDomain) Error 'The domain:{domain} does not exist.';
                if (existsDomain)
                {{
                    existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				    Check(existKind) Error 'The request kind:{kind} does not exist.';

                    if (existKind)
                    {{
                        domain = company.Sales.DomainFrom('{domain}');
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                        Check(existPaymentDock) Error 'The payment engine dock for the domain:{domain} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                domain = company.Sales.DomainFrom('{domain}');
                depositId = {depositId};
                draftDepositConfirmation = liquid.Source.CreateDraftDeposit(itIsThePresent, Now, depositId, '{invoiceId}', {depositAuthorization}, '{atAddress}', {externalReference}, '{invoiceDestination}', {exchangeAmount}, {exchangeRate}, '{confirmedCurrency}', {confirmedAmount}, {totalConfirmations}, {storeId}, domain);
                draftDepositConfirmation.PaymentLink = '{invoicePaymentLink}';
                print draftDepositConfirmation.Id depositId;
                print draftDepositConfirmation.AuthorizationId authorizationId;
                print draftDepositConfirmation.InvoiceId invoiceId;
            }}");
            return result;
        }

        private async Task<int> CreditDepositAndLockAsync(string kind, NextDepositDTO nexDepositData, string atAddress, string who, decimal exchangeAmount, decimal exchangeRate)
        {
            int depositAuthorization = -1;
            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(nexDepositData.AgentId, nexDepositData.DomainUrl);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, kind, PaymentMethod.ThirdParty, entityId: 4);//Drivers: entityId 4 means FIERO segun Driver de Deposit
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("amount", exchangeAmount);
                recordSet.SetParameter("accountNumber", kind);
                recordSet.SetParameter("currency", kind);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("domain", nexDepositData.DomainUrl);
                recordSet.SetParameter("sourceNumber", 17);
                recordSet.SetParameter("sourceName", GamesEngine.Finance.TransactionMessage.NO_SOURCE_NAME);
                recordSet.SetParameter("who", who);
                recordSet.SetParameter("agent", nexDepositData.AgentId);
                recordSet.SetParameter("storeId", nexDepositData.StoreId);
                recordSet.SetParameter("processorId", paymentProcessor.Id);
                recordSet.SetParameter("description", $"Confirmed and Locked deposit {nexDepositData.DepositId} from {nexDepositData.DomainUrl}");
                recordSet.SetParameter("reference", nexDepositData.DepositId);
                recordSet.SetParameter("withLock", true);

                var driverResult = await paymentProcessor.ExecuteAsync<DepositTransaction>(DateTime.Now, recordSet);
                if (driverResult.Status != Status.Ok)
                {
                    _logger.LogError($"Failed to credit deposit and lock. Status: {driverResult.Status}, Message: {driverResult.ErrorMessage}");
                    return ASITenantDriver.FAKE_TICKET_NUMBER; // Return a fake ticket number to indicate failure
                }

                depositAuthorization = driverResult.Data.AuthorizationId;
            }
            return depositAuthorization;
        }

        [HttpGet("api/liquidity/{kind}/deposit/{depositId}/invoice")]
        public async Task<IActionResult> InvoceDepositAsync(string kind, int depositId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDeposit = liquid.Source.Jar.ExistDeposit({depositId});
                    Check(existDeposit) Error 'The deposit:{depositId} does not exist.';
                    if (existDeposit)
                    {{
                        enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit({depositId});  

                        validDepositType = enclosureDeposit.IsPending;
                        Check(validDepositType) Error 'The deposit:{depositId} is not a pending type.';
                    }}                    
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                deposit = liquid.Source.Jar.FindDepositById({depositId});
                
                print deposit.Id id;
                print deposit.InvoiceId invoiceId;
                print deposit.Destination destination;
                print deposit.PaymentLink paymentLink;
                print deposit.Amount amount;
                print deposit.Rate rate;
            }}");
            return result;
        }

        private async Task<IActionResult> ConfirmDepositAsync(ConfirmedDepositDueBody body)
        {
            if (string.IsNullOrWhiteSpace(body.Kind)) return BadRequest("kind is required");
            if (body.DepositId <= 0) return BadRequest("depositId must be greater than zero");
            if (body == null) return BadRequest("body is required");
            if (body.Due < 0) return BadRequest("Due is required");

            var kind = body.Kind;
            var depositId = body.DepositId;
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var depositDataQuery = await RetriveDepositDataAsync(kind, depositId, resultRate.Value);
            if (!(depositDataQuery is OkObjectResult)) return depositDataQuery;
            OkObjectResult o = (OkObjectResult)depositDataQuery;
            string json = o.Value.ToString();
            var deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
            if (deposit == null) return BadRequest("Failed to retrieve deposit data.");
            if (!deposit.IsPending) return BadRequest($"The deposit {depositId} is not in a pending state. Current state: {(deposit.IsConfirmed ? "Confirmed" : "Canceled")}");

            //string who = Security.UserName(HttpContext);
            string who = "Liquidity";

            DueStatus status = DueStatus.Paid;
            //SI EL PENDIENTE NO ES 0, SE DEBE HACER UN REEMBOLSO
            if (body.Due != 0)
            {
                var resetDepositResult = await UnlockDebitMovement(kind, deposit, who);
                if (!(resetDepositResult is OkObjectResult)) return resetDepositResult;

                if (body.Due < 0)//Overpaid
                {
                    status = DueStatus.Overpaid;
                }
                else if (body.Due > 0)//Underpaid
                {
                    status = DueStatus.Underpaid;
                }
                int newOverpaidDepositId = await RemakeDeposit(
                    depositId,
                    kind,
                    deposit.DomainUrl,
                    deposit.InvoiceId,
                    deposit.Destination,
                    deposit.PaymentLink,
                    deposit.ExternalAtAddress,
                    deposit.ExternalReference,
                    who,
                    body.TotalPaid,
                    body.Rate,
                    totalConfirmations: 0,
                    deposit.ConfirmedCurrency,
                    deposit.StoreId
                );
                
                depositDataQuery = await RetriveDepositDataAsync(kind, newOverpaidDepositId,resultRate.Value);
                if (!(depositDataQuery is OkObjectResult)) return depositDataQuery;
                o = (OkObjectResult)depositDataQuery;
                json = o.Value.ToString();
                deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
                if (deposit == null) return BadRequest("Failed to retrieve deposit data.");
                if (!deposit.IsPending) return BadRequest($"The deposit {newOverpaidDepositId} is still in a pending state after remaking the deposit movements.");
            }

            MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(deposit.AgentId, deposit.DomainUrl);
            var entityId = 4; // fiero
            PaymentProcessor refundProcessor = multipleDrivers.SearchByX(TransactionType.Unlock, kind, PaymentMethod.ThirdParty, entityId);
            if (refundProcessor == null) return BadRequest($"No unlock processor found for kind: {kind} and entityId: {entityId}.");

            bool unlockDepositResult = false;
            using (RecordSet recordSet = refundProcessor.GetRecordSet())
            {
                recordSet.SetParameter("atAddress", deposit.ExternalAtAddress);
                recordSet.SetParameter("amount", deposit.Amount);
                recordSet.SetParameter("accountNumber", kind);
                recordSet.SetParameter("who", who);
                recordSet.SetParameter("documentNumber", deposit.AuthorizationId);
                recordSet.SetParameter("storeId", deposit.StoreId);
                recordSet.SetParameter("concept", $"Unlocked deposit {deposit.Id} from {deposit.DomainUrl}");
                recordSet.SetParameter("reference", deposit.Id);
                recordSet.SetParameter("processorId", refundProcessor.Id);
                var driverResult = await refundProcessor.ExecuteAsync<DoneResponse>(DateTime.Now, recordSet);
                if (driverResult == null) return BadRequest("Failed to create the refund transaction. The driver result is null.");
                if (driverResult.Status != Status.Ok) return BadRequest($"Failed to create the refund transaction. The status is {driverResult.Status}.");
                unlockDepositResult = driverResult.Data.Done;
            }
            if (!unlockDepositResult) return BadRequest("Failed to unlock the deposit. The refund was not successful.");

            string channel = "Deposit";
            string eventType = $"Deposit.{kind}.Approve";
            var externalHookProperties = new Dictionary<string, object>
            {
                { "reference", deposit.ExternalReference },
                { "atAddress", deposit.ExternalAtAddress },
                { "status", $"{status}" },
                { "amount", body.TotalPaid * body.Rate },
                { "storeId", deposit.StoreId },
                { "depositId", deposit.Id}
            };
            if (WebHookClient.IsConfigured)
            {
                _ = WebHookClient.Instance.SendWebHookAsync(DateTime.Now, externalHookProperties, eventType, channel);
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                    existDeposit = liquid.Source.Jar.ExistDeposit({deposit.Id});
                    Check(existDeposit) Error 'The deposit:{deposit.Id} does not exist.';
                    if (existDeposit)
                    {{
                        enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit({deposit.Id});  

                        validDepositType = enclosureDeposit.IsPending;
                        Check(validDepositType) Error 'The deposit:{deposit.Id} is not a pending type.';

                        domain = company.Sales.DomainFrom('{deposit.DomainUrl}');
                        existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                        Check(existPaymentDock) Error 'The payment engine dock for the domain:{deposit.DomainUrl} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                deposit = liquid.Source.Jar.FindDepositById({deposit.Id});
                confirmedDeposit = liquid.Source.ConfirmDeposit(itIsThePresent, Now, deposit);
                print confirmedDeposit.Id id;
            }}");

            return result;
        }

        private async Task<IActionResult> UnlockDebitMovement(string kind, RetrieveDepositData depositData, string who)
        {
            MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(depositData.AgentId, depositData.DomainUrl);
            var entityId = 4; // fiero
            PaymentProcessor unlockAndDebitProcessor = multipleDrivers.SearchByX(TransactionType.UnlockDebit, kind, PaymentMethod.ThirdParty, entityId);
            if (unlockAndDebitProcessor == null) return BadRequest($"No unlock and withdrawal processor found for kind: {kind} and entityId: {entityId}.");

            using (RecordSet recordSet = unlockAndDebitProcessor.GetRecordSet())
            {
                recordSet.SetParameter("atAddress", depositData.ExternalAtAddress);
                recordSet.SetParameter("amount", depositData.Amount);
                recordSet.SetParameter("accountNumber", kind);
                recordSet.SetParameter("who", who);
                recordSet.SetParameter("sourceNumber", 17);
                recordSet.SetParameter("sourceName", GamesEngine.Finance.TransactionMessage.NO_SOURCE_NAME);
                recordSet.SetParameter("documentNumber", depositData.AuthorizationId);
                recordSet.SetParameter("storeId", depositData.StoreId);
                recordSet.SetParameter("concept", $"Unlocked and debited deposit {depositData.Id} from {depositData.DomainUrl}");
                recordSet.SetParameter("reference", depositData.Id);
                recordSet.SetParameter("processorId", unlockAndDebitProcessor.Id);
                var driverResult = await unlockAndDebitProcessor.ExecuteAsync<DoneResponse>(DateTime.Now, recordSet);
                if (driverResult == null) return BadRequest("Failed to create the refund transaction. The driver result is null.");
                if (driverResult.Status != Status.Ok) return BadRequest($"Failed to create the refund transaction. The status is {driverResult.Status}.");

                return Ok(new
                {
                    Done = driverResult.Data.Done,
                    Message = "Deposit lock reinitialized successfully."
                });
            }
        }

        private async Task<int> RemakeDeposit(int depositId, string kind, string domainUrl, string invoiceId, string destination, string paymentLink, string externalAtAddress, int externalReference, string who, decimal totalPayedAmount, decimal rate, int totalConfirmations, string confirmedCurrency, int storeId)
        {
            var cancelOldDepositCmd = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                    existDeposit = liquid.Source.Jar.ExistDeposit({depositId});
                    if (existDeposit)
                    {{
                        enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit({depositId});
                        validDepositType = enclosureDeposit.IsPending;
                        Check(validDepositType) Error 'The deposit:{depositId} is not a pending type.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                deposit = liquid.Source.Jar.FindDepositById({depositId});
                canceledDeposit = liquid.Source.Jar.CancelDeposit(itIsThePresent, now, deposit);
                print canceledDeposit.Id id;
            }}");
            if (!(cancelOldDepositCmd is OkObjectResult)) throw new GameEngineException($"Failed to cancel the old deposit id {depositId} command.");

            var newOverpaidDeposit = await RetrieveNextDepositAsync(kind, domainUrl);
            if (!(newOverpaidDeposit is OkObjectResult)) throw new GameEngineException($"Failed to retrieve next deposit for kind: {kind} and domain: {domainUrl}.");
            OkObjectResult o = (OkObjectResult)newOverpaidDeposit;
            string json = o.Value.ToString();
            var newOverpaidDepositData = JsonConvert.DeserializeObject<NextDepositDTO>(json);
            if (newOverpaidDepositData == null) throw new GameEngineException("Failed to retrieve next deposit data for Overpaid deposit.");

            int newDepositAuthorization = await CreditDepositAndLockAsync(kind, newOverpaidDepositData, externalAtAddress, who, totalPayedAmount, rate);
            if (newDepositAuthorization == ASITenantDriver.FAKE_TICKET_NUMBER) throw new GameEngineException("Could not get a valid authorization ID for the new Overpaid deposit.");

            decimal newConfirmedAmount = totalPayedAmount * rate; // Rubicon: El monto confirmado es el monto pagado por la tasa de cambio actual.

            var depositCommand = await CreateDraftDepositAsync(
                kind,
                domainUrl,
                externalAtAddress,
                newOverpaidDepositData.DepositId,
                invoiceId,
                newDepositAuthorization,
                externalReference,
                destination,
                paymentLink,
                totalPayedAmount,
                rate,
                confirmedCurrency,
                newConfirmedAmount,
                totalConfirmations,
                storeId
            );
            if (!(depositCommand is OkObjectResult)) throw new GameEngineException("Failed to create the new deposit command.");




            return newOverpaidDepositData.DepositId;
        }

        [DataContract(Name = "GenericHook")]
        public class GenericHook
        {
            [DataMember(Name = "type")]
            public string Type { get; set; }
            [DataMember(Name = "storeId")]//NOT THE SAME AS GAMEENGINE is BTCPAY Store ID
            public string StoreId { get; set; }
        }

        [DataContract(Name = "ConfirmInvoiceHook")]
        public class ConfirmInvoiceHook : GenericHook
        {
            [DataMember(Name = "invoiceId")]
            public string InvoiceId { get; set; }
        }

        [DataContract(Name = "InvoiceExpiredHook")]
        public class InvoiceExpiredHook : GenericHook
        {
            [DataMember(Name = "invoiceId")]
            public string InvoiceId { get; set; }
        }

        [DataContract(Name = "PayoutUpdatedHook")]
        public class PayoutUpdatedHook : GenericHook
        {
            [DataMember(Name = "payoutId")]
            public string PayoutId { get; set; }
            [DataMember(Name = "pullPaymentId")]
            public string PullPaymentId { get; set; }
            [DataMember(Name = "payoutState")]
            public string PayoutState { get; set; }
        }

        [HttpPost("api/liquidity/payment/events")]
        public async Task<IActionResult> PaymentEventsAsync()
        {
            HttpContext.Request.EnableBuffering();
            HttpContext.Request.Body.Position = 0;
            using var reader = new StreamReader(HttpContext.Request.Body, Encoding.UTF8, leaveOpen: true);
            string payload = await reader.ReadToEndAsync();
            HttpContext.Request.Body.Position = 0;

            //Rubicon: Por ahora no se valida la firma del webhook, logica ya se implemento
            //if (!HttpContext.Request.Headers.TryGetValue("btcpay-sig", out var signature) || string.IsNullOrEmpty(signature)) return BadRequest("Signature header is required");
            //bool checkSignature = await InvoicePaymentSettings.ValidSignatureAsync(payload, signature);
            //if (!checkSignature) return BadRequest("Invalid signature for the invoice confirmation.");

            string kind = "BTC";//Rubicon: Por ahora solo se permite confirmar invoices de tipo BTC. porque no se pueden crear url dinamicas para otros tipos de monedas.
            GenericHook genericBody = JsonConvert.DeserializeObject<GenericHook>(payload);
            if (genericBody == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(genericBody.Type))
            {
                Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: genericBody.Type is null or empty. Payload: {payload}");
                return BadRequest("type is required");
            }
            if (string.IsNullOrWhiteSpace(genericBody.StoreId))
            {
                Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: genericBody.StoreId is null or empty. Payload: {payload}");
                return BadRequest("storeId is required");
            }
            var paymentDockId = genericBody.StoreId;

            string who = "Liquidity";

            switch (genericBody.Type)
            {
                case "InvoiceReceivedPayment":
                    ConfirmInvoiceHook confirmHookBody = JsonConvert.DeserializeObject<ConfirmInvoiceHook>(payload);
                    if (confirmHookBody == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: confirmHookBody is null. Payload: {payload}");
                        return BadRequest("body is required");
                    }
                    if (string.IsNullOrWhiteSpace(confirmHookBody.InvoiceId))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: confirmHookBody.InvoiceId is null or empty. Payload: {payload}");
                        return BadRequest("invoiceId is required");
                    }

                    var invoicePayment = await PaymentManager.InvoicePaymentMethodAsync(confirmHookBody.InvoiceId);
                    if (invoicePayment == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoicePayment is null for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"Failed to retrieve payment methods for invoice {confirmHookBody.InvoiceId}.");
                    }
                    if (invoicePayment.Payments == null || invoicePayment.Payments.Count == 0)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoicePayment.Payments is null or empty for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The invoice {confirmHookBody.InvoiceId} does not have any payments.");
                    }

                    var lastPayment = invoicePayment.Payments.LastOrDefault();
                    if (lastPayment == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: lastPayment is null for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The invoice {confirmHookBody.InvoiceId} does not have any payments.");
                    }
                    if (string.IsNullOrWhiteSpace(lastPayment.Id))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: lastPayment.Id is null or empty for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The invoice {confirmHookBody.InvoiceId} does not have a valid payment ID.");
                    }
                    if (!decimal.TryParse(invoicePayment.Due, out decimal invoiceDue))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoicePayment.Due is not a valid decimal for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The invoice {confirmHookBody.InvoiceId} does not have a valid amount.");
                    }
                    if (!decimal.TryParse(invoicePayment.TotalPaid, out decimal totalPaid))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoicePayment.TotalPaid is not a valid decimal for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The invoice {confirmHookBody.InvoiceId} has not been fully paid. Total paid: {totalPaid}.");
                    }
                    if (!decimal.TryParse(invoicePayment.Rate, out decimal rate))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoicePayment.Rate is not a valid decimal for invoiceId {confirmHookBody.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The invoice {confirmHookBody.InvoiceId} does not have a valid rate.");
                    }

                    var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
                    {{
                        existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				        Check(existKind) Error 'The request kind:{kind} does not exist.';
                        if (existKind)
                        {{
                            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                            existInvoice = liquid.IngressSentinel.ExistInvoiceInDeposits('{confirmHookBody.InvoiceId}');
                            Check(existInvoice) Error 'The invoice:{confirmHookBody.InvoiceId} does not exist in Sentinel pending the deposits.';
                        }}
                    }}
                    ", $@"
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        liquid.IngressSentinel.InvoiceReceivedPayment(itIsThePresent, now, '{confirmHookBody.InvoiceId}', {invoiceDue}, {totalPaid}, {rate}, '{lastPayment.Id}', '{confirmHookBody.StoreId}');
                    }}");

                    if (!(result is OkObjectResult)) 
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Failed to process invoice payment for invoiceId {confirmHookBody.InvoiceId}. Result: {result}. Payload: {payload}");
                        return BadRequest($"Failed to process the payment for invoice {confirmHookBody.InvoiceId}.");
                    }

                    return result;
                case "InvoiceExpired":
                    InvoiceExpiredHook invoiceExpiredHook = JsonConvert.DeserializeObject<InvoiceExpiredHook>(payload);
                    if (invoiceExpiredHook == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoiceExpiredHook is null. Payload: {payload}");
                        return BadRequest("body is required");
                    }
                    if (string.IsNullOrWhiteSpace(invoiceExpiredHook.InvoiceId))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: invoiceExpiredHook.InvoiceId is null or empty. Payload: {payload}");
                        return BadRequest("invoiceId is required");
                    }

                    var resultDeposit = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
                    {{
                        existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                        Check(existKind) Error 'The request kind:{kind} does not exist.';
                        if (existKind)
                        {{
                            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                            existInvoice = liquid.IngressSentinel.ExistInvoiceInDeposits('{invoiceExpiredHook.InvoiceId}');
                        }}
                    }}", $@"
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        deposit = liquid.IngressSentinel.FindDepositByInvoice('{invoiceExpiredHook.InvoiceId}');
                        print deposit.Id depositId;
                    }}");

                    if (!(resultDeposit is OkObjectResult))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Failed to retrieve deposit for expired invoiceId {invoiceExpiredHook.InvoiceId}. Result: {resultDeposit}. Payload: {payload}");
                        return BadRequest("Failed to retrieve the deposit associated with the expired invoice.");
                    }

                    OkObjectResult o = (OkObjectResult)resultDeposit;
                    string json = o.Value.ToString();
                    var depositInfo = JsonConvert.DeserializeObject<DepositDTO>(json);
                    if (depositInfo == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: depositInfo is null for expired invoiceId {invoiceExpiredHook.InvoiceId}. Payload: {payload}");
                        return BadRequest("Failed to retrieve deposit info from the canceled deposit.");
                    }

                    var depositDataQuery = await RetriveDepositDataAsync(kind, depositInfo.DepositId);
                    if (!(depositDataQuery is OkObjectResult))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Failed to retrieve deposit data for depositId {depositInfo.DepositId} and expired invoiceId {invoiceExpiredHook.InvoiceId}. Result: {depositDataQuery}. Payload: {payload}");
                        return depositDataQuery;
                    }

                    o = (OkObjectResult)depositDataQuery;
                    json = o.Value.ToString();
                    var deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
                    if (deposit == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: deposit is null for depositId {depositInfo.DepositId} and expired invoiceId {invoiceExpiredHook.InvoiceId}. Payload: {payload}");
                        return BadRequest("Failed to retrieve deposit data.");
                    }
                    if (deposit.IsCanceled)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: deposit {deposit.Id} is already canceled for expired invoiceId {invoiceExpiredHook.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The deposit {deposit.Id} is already canceled.");
                    }
                    if (deposit.IsConfirmed)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: deposit {deposit.Id} is already confirmed for expired invoiceId {invoiceExpiredHook.InvoiceId}. Payload: {payload}");
                        return BadRequest($"The deposit {deposit.Id} is already confirmed.");
                    }

                    var unlockCashier = await UnlockDebitMovement(kind, deposit, who);
                    if (!(unlockCashier is OkObjectResult))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Failed to unlock deposit {deposit.Id} for expired invoiceId {invoiceExpiredHook.InvoiceId}. Result: {unlockCashier}. Payload: {payload}");
                        return BadRequest("Failed to unlock the deposit after invoice expiration.");
                    }

                    var resultInvoiceExpired = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
                    {{
                        existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                        Check(existKind) Error 'The request kind:{kind} does not exist.';
                        if (existKind)
                        {{
                            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                            existInvoice = liquid.IngressSentinel.ExistInvoiceInDeposits('{invoiceExpiredHook.InvoiceId}');
                            Check(existInvoice) Error 'The invoice:{invoiceExpiredHook.InvoiceId} does not exist in Sentinel pending the deposits.';
                        }}
                    }}", $@"
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        deposit = liquid.IngressSentinel.FindDepositByInvoice('{invoiceExpiredHook.InvoiceId}');
                        canceledDeposit = liquid.Source.Jar.CancelDeposit(itIsThePresent, now, deposit);
                    }}");

                    if (!(resultInvoiceExpired is OkObjectResult))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Failed to cancel deposit {deposit.Id} for expired invoiceId {invoiceExpiredHook.InvoiceId}. Result: {resultInvoiceExpired}. Payload: {payload}");
                        return BadRequest("Failed to cancel the deposit after invoice expiration.");
                    }

                    var body = new Dictionary<string, object>
                    {
                        { "sourceName", kind },
                        { "reference", deposit.Id },
                        { "atAddress", deposit.ExternalAtAddress },
                        { "reason", "The invoice for your deposit has expired." }
                    };

                    string channel = "Deposit";
                    string eventType = $"{channel}.{kind}.Cancel";
                    if (WebHookClient.IsConfigured)
                    {
                        _ = WebHookClient.Instance.SendWebHookAsync(DateTime.Now, body, eventType, channel);
                    }

                    return resultInvoiceExpired;
                case "PayoutUpdated":
                    PayoutUpdatedHook payoutUpdatedHook = JsonConvert.DeserializeObject<PayoutUpdatedHook>(payload);
                    if (payoutUpdatedHook == null)
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: payoutUpdatedHook is null. Payload: {payload}");
                        return BadRequest("body is required");
                    }
                    if (string.IsNullOrWhiteSpace(payoutUpdatedHook.PayoutId))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: payoutUpdatedHook.PayoutId is null or empty. Payload: {payload}");
                        return BadRequest("payoutId is required");
                    }
                    if (string.IsNullOrWhiteSpace(payoutUpdatedHook.PullPaymentId))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: payoutUpdatedHook.PullPaymentId is null or empty. Payload: {payload}");
                        return BadRequest("pullPaymentId is required");
                    }
                    if (payoutUpdatedHook.PayoutState != "Completed")
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: payoutUpdatedHook.PayoutState is not Completed. Current state: {payoutUpdatedHook.PayoutState}. Payload: {payload}");
                        return BadRequest("payoutState must be Completed");
                    }

                    var resultPaymentUpdated = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
                    {{
                        existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				        Check(existKind) Error 'The request kind:{kind} does not exist.';
                        if (existKind)
                        {{
                            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                            existWithdrawal = liquid.Outlet.ExistWithdrawalWithPullPaymentId('{payoutUpdatedHook.PullPaymentId}');
                            Check(existWithdrawal) Error 'The withdrawal with PullPaymentId:{payoutUpdatedHook.PullPaymentId} does not exist.';
                            if (existWithdrawal)
                            {{
                                withdrawal = liquid.Outlet.FindWithdrawalFromPullPaymentId('{payoutUpdatedHook.PullPaymentId}');
                                existDispenser = liquid.Outlet.ExistDispenserFromWithdrawal(withdrawal);
                                Check(existDispenser) Error 'The dispenser for the withdrawal with PullPaymentId:{payoutUpdatedHook.PullPaymentId} does not exist.';
                                if (existDispenser)
                                {{
                                    dispenser = liquid.Outlet.FindDispenserFromWithdrawal(withdrawal);
                                    validDispenserType = dispenser.Type == '{typeof(DispenserReady).Name}';
                                    Check(validDispenserType) Error 'The dispenser for the withdrawal with PullPaymentId:{payoutUpdatedHook.PullPaymentId} is not a ready type.';
                                }}

                                existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(withdrawal.Domain);
                                Check(existPaymentDock) Error 'The payment engine dock for the domain ' + withdrawal.Domain + ' does not exist.';
                                if (existPaymentDock)
                                {{
                                    paymentDock = liquid.ParentFlow.FindPaymentEngineDock(withdrawal.Domain);
                                    validPaymentDock = paymentDock.Id == '{paymentDockId}';
                                    Check(validPaymentDock) Error 'The payment engine dock for the domain ' + withdrawal.Domain + ' does not match the provided PaymentDockId: {paymentDockId}.';
                                }}  
                            }}
                        }}
                    }}
                    ", $@"
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        withdrawal = liquid.Outlet.FindWithdrawalFromPullPaymentId('{payoutUpdatedHook.PullPaymentId}');
                        dispenser = liquid.Outlet.FindDispenserFromWithdrawal(withdrawal);
                        dispenser.ConfirmWithdrawal(now, withdrawal, '{payoutUpdatedHook.PayoutId}', '{paymentDockId}');
                        liquid.Outlet.AddConfirmedWithdrawalToSliding(now, withdrawal);
                    }}");

                    if (!(resultPaymentUpdated is OkObjectResult))
                    {
                        Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Failed to process payout update for PullPaymentId {payoutUpdatedHook.PullPaymentId}. Result: {resultPaymentUpdated}. Payload: {payload}");
                        return BadRequest($"Failed to process the payout update for PullPaymentId {payoutUpdatedHook.PullPaymentId}.");
                    }

                    return resultPaymentUpdated;
                default:
                    Loggers.GetIntance().Sentinel.Debug($"LiquidityAPI: InvoiceReceivedPaymentAsync: Unsupported hook type {genericBody.Type}. Payload: {payload}");
                    return BadRequest($"Unsupported hook type: {genericBody.Type}");
            }
        }

        [HttpGet("api/liquidity/{kind}/paymentDock")]
        public async Task<IActionResult> GetPaymentDockAsync(string kind, string domainUrl)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (string.IsNullOrWhiteSpace(domainUrl)) return BadRequest("DomainUrl is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDomain = company.Sales.ExistsDomain('{domainUrl}');
                    Check(existDomain) Error 'The domain:{domainUrl} does not exist.';
                    if (existDomain)
                    {{
                        domain = company.Sales.DomainFrom('{domainUrl}');
                        existPaymentStore = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                        Check(existPaymentStore) Error 'The Payment StoreId for the domain:{domainUrl} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                domain = company.Sales.DomainFrom('{domainUrl}');
                paymentDock = liquid.ParentFlow.FindPaymentEngineDock(domain);
                print paymentDock.Dock paymentDockId;
            }}");
            return result;
        }

        public class CreatePaymentDockBody
        {
            [DataMember(Name = "paymentDockId")]
            public string PaymentDockId { get; set; }
            [DataMember(Name = "DomainUrl")]
            public string DomainUrl { get; set; }
        }

        [HttpPost("api/liquidity/{kind}/paymentDock")]
        public async Task<IActionResult> CreatePaymentDockAsync(string kind, [FromBody] CreatePaymentDockBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.PaymentDockId)) return BadRequest("PaymentDockId is required");
            if (string.IsNullOrWhiteSpace(body.DomainUrl)) return BadRequest("DomainUrl is required");
            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDomain = company.Sales.ExistsDomain('{body.DomainUrl}');
                    Check(existDomain) Error 'The domain:{body.DomainUrl} does not exist.';
                    if (existDomain)
                    {{
                        domain = company.Sales.DomainFrom('{body.DomainUrl}');
                        existPaymentStore = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                        Check(!existPaymentStore) Error 'The Payment StoreId:{body.PaymentDockId} already exists for the domain:{body.DomainUrl}.';
                    }}
                }}
            }}
            ", $@"
            {{
                domain = company.Sales.DomainFrom('{body.DomainUrl}');
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                paymentDock = liquid.ParentFlow.AddPaymentDock(domain, '{body.PaymentDockId}');
            }}");
            return result;
        }

        [HttpPost("api/liquidity/{kind}/deposit/{depositId}/inprocess")]
        public async Task<IActionResult> InprocessInvoiceAsync( string kind,  int depositId)
        {
            if (depositId <= 0) return BadRequest("depositId is required and must be greater than zero.");
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var depositDataQuery = await RetriveDepositDataAsync(kind, depositId, resultRate.Value);
            if (depositDataQuery is not OkObjectResult o || o.Value == null) return depositDataQuery;

            var depositData = o.Value as RetrieveDepositData;
            if (depositData == null)
            {
                if (o.Value is string jsonStr)
                    depositData = JsonConvert.DeserializeObject<RetrieveDepositData>(jsonStr);
                else
                    depositData = JsonConvert.DeserializeObject<RetrieveDepositData>(o.Value.ToString());
            }
            if (depositData == null) return BadRequest("Failed to retrieve deposit data.");
            if (!depositData.IsPending) return BadRequest($"The deposit {depositId} is not in a pending state. Current state: {(depositData.IsConfirmed ? "Confirmed" : "Canceled")}");

            string eventType = $"Deposit.{kind}.Inprocess";
            string channel = "Deposit";

            var invoicePayment = await PaymentManager.InvoicePaymentMethodAsync(depositData.InvoiceId);
            if (!decimal.TryParse(invoicePayment.Due, out decimal invoiceDue)) return BadRequest($"The invoice {depositData.InvoiceId} does not have a valid amount.");
            if (!decimal.TryParse(invoicePayment.TotalPaid, out decimal totalPaid)) return BadRequest($"The invoice {depositData.InvoiceId} has not been fully paid. Total paid: {totalPaid}.");
            if (!decimal.TryParse(invoicePayment.Rate, out decimal rate)) return BadRequest($"The invoice {depositData.InvoiceId} does not have a valid rate.");

            DueStatus status = DueStatus.NotSet;
            switch (invoiceDue)
            {
                case 0:
                    status = DueStatus.Paid;
                    break;
                case < 0:
                    status = DueStatus.Overpaid;
                    break;
                case > 0:
                    status = DueStatus.Underpaid;
                    break;
            }

            var body = new Dictionary<string, object>
            {
                { "sourceName", kind },
                { "reference", depositData.ExternalReference },
                { "atAddress", depositData.ExternalAtAddress },
                { "status", $"{status}" },
                { "amount", totalPaid * rate }
            };

            if (WebHookClient.IsConfigured)
            {
                _ = WebHookClient.Instance.SendWebHookAsync(DateTime.Now, body, eventType, channel);
            }

            return Ok();
        }

        [DataContract(Name = "DraftConfirmationBody")]
        public class ManualConfirmDepositBody
        {
            [DataMember(Name = "depositId")]
            public int DepositId { get; set; }

            [DataMember(Name = "DepositBI")]
            public DepositBI DepositBI { get; set; }
        }

        [DataContract(Name = "DepositBI")]
        public class DepositBI
        {
            [DataMember(Name = "atAddress")]
            public string AtAddress { get; set; }
            [DataMember(Name = "paidAmount")]
            public decimal PaidAmount { get; set; }
            [DataMember(Name = "currency")]
            public string Currency { get; set; }
        }

        [HttpPost("api/liquidity/{kind}/deposit/unassigned/manual/confirm")]
        public async Task<IActionResult> ManualConfirmDepositMatchAsync(string kind, [FromBody] ManualConfirmDepositBody biConfirmanBody)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (biConfirmanBody == null) return BadRequest("body is required");
            if (biConfirmanBody.DepositId <= 0) return BadRequest("depositId must be greater than zero");
            if (biConfirmanBody.DepositBI == null) return BadRequest("DepositBI is required");
            if (string.IsNullOrWhiteSpace(biConfirmanBody.DepositBI.AtAddress)) return BadRequest("AtAddress is required");
            if (biConfirmanBody.DepositBI.PaidAmount <= 0) return BadRequest("PaidAmount must be greater than zero");
            if (string.IsNullOrWhiteSpace(biConfirmanBody.DepositBI.Currency)) return BadRequest("Currency is required");
            if (kind != biConfirmanBody.DepositBI.Currency) return BadRequest($"The kind {kind} does not match the currency {biConfirmanBody.DepositBI.Currency}.");

            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");

            var depositDataQuery = await RetriveDepositDataAsync(kind, biConfirmanBody.DepositId,resultRate.Value);
            if (!(depositDataQuery is OkObjectResult)) return depositDataQuery;
            OkObjectResult o = (OkObjectResult)depositDataQuery;
            string json = o.Value.ToString();
            var currentDeposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);

            if (!currentDeposit.IsPending) return BadRequest($"The deposit {biConfirmanBody.DepositId} is not in a pending state.");
            if (currentDeposit.ExternalAtAddress != biConfirmanBody.DepositBI.AtAddress) return BadRequest($"The AtAddress {biConfirmanBody.DepositBI.AtAddress} does not match the deposit's AtAddress {currentDeposit.ExternalAtAddress}.");

            var dueAmount = currentDeposit.Amount - biConfirmanBody.DepositBI.PaidAmount;
            ConfirmedDepositDueBody confirmanBody = new ConfirmedDepositDueBody
            (
                kind: kind,
                depositId: biConfirmanBody.DepositId,
                totalPaid: biConfirmanBody.DepositBI.PaidAmount,
                rate: currentDeposit.Rate,
                due : dueAmount
            );
            var result = await ConfirmDepositAsync(confirmanBody);
            return result;
        }

        [HttpPost("api/liquidity/{kind}/tank")]
        public async Task<IActionResult> CreateTankAsync(string kind, [FromBody] CreateTankBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("kind is required");
            if(body.Deposits == null || body.Deposits.Count == 0) return BadRequest("deposits is required");

            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty : Validator.StringEscape(body.Description);
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);

            StringBuilder cmdCreateTank = new StringBuilder();
            StringBuilder cmdCheck= new StringBuilder();
            if (body.Deposits == null || body.Deposits.Count == 0)
            {
                if (isDescriptionNullOrEmpty)
                {
                    cmdCreateTank.AppendLine($"tank = source.Jar.CreateTank(itIsThePresent, Now, tankId, '{escapedName}','', newJarVersion);");
                    
                }
                else
                {
                   cmdCreateTank.AppendLine($"tank = source.Jar.CreateTank(itIsThePresent, Now, tankId, '{escapedName}','{escapedDescription}', newJarVersion);");
                }
            }
            else
            {
                var depositIds = string.Join(",", body.Deposits);
                if (isDescriptionNullOrEmpty)
                {
                    cmdCreateTank.AppendLine($"tank = source.Jar.CreateTank(itIsThePresent, Now, tankId, '{escapedName}','', newJarVersion, {{{depositIds}}});");
                }
                else
                {
                    cmdCreateTank.AppendLine($"tank = source.Jar.CreateTank(itIsThePresent, Now, tankId, '{escapedName}','{escapedDescription}', newJarVersion, {{{depositIds}}});");
                }
            }

            if (!string.IsNullOrWhiteSpace(body.Color))
            {
                cmdCreateTank.AppendLine($"tank.ChangeColor('{body.Color}');");
            }
            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }

                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append($"isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdCreateTank.AppendLine($"tank.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }

            if (body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && !string.IsNullOrEmpty(body.CurrencyCode))
            {
                cmdCreateTank.AppendLine($"tank.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                     {cmdCheck}   
                    for (sources : liquid.Sources)
                    {{
                        source = sources;
                        hasConfirmedDeposit = source.Jar.ConfirmedDeposits().Count() > 0;
				        Check(hasConfirmedDeposit) Error 'There are no confirm deposits for create a tank.';
                    }}

                    //Rubicon: Verificar que los id de los deposits existen en el source
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;
                    Eval('tankId = '+source.NextTankId()+';');
                    Eval('newJarVersion = '+source.NextJarVersion()+';');
                    {cmdCreateTank}
                    print tank.Id tankId;
                }}

            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/empty/tank")]
        public async Task<IActionResult> CreateEmptyTankAsync(string kind, [FromBody] CreateTankBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("kind is required");
            if(body.GoalAmount.HasValue && body.GoalAmount.Value < 0) return BadRequest("targetAmount must be greater than zero");
            if(body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest("currencyCode is required when targetAmount is provided");

            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty : Validator.StringEscape(body.Description);
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);
            StringBuilder cmdCreateTank = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            if (isDescriptionNullOrEmpty)
            {
                cmdCreateTank.AppendLine($"tank = source.Jar.CreateEmptyTank(itIsThePresent, Now, tankId, '{escapedName}','', newJarVersion);");
            }
            else
            {
                cmdCreateTank.AppendLine($"tank = source.Jar.CreateEmptyTank(itIsThePresent, Now, tankId, '{escapedName}','{escapedDescription}', newJarVersion);");
            }

            if (!string.IsNullOrWhiteSpace(body.Color))
            {
                cmdCreateTank.AppendLine($"tank.ChangeColor('{body.Color}');");
            }

            if(body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && !string.IsNullOrEmpty(body.CurrencyCode))
            {
                cmdCreateTank.AppendLine($"tank.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }

                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append($"isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdCreateTank.AppendLine($"tank.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {cmdCheck}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('tankId = '+source.NextTankId()+';');
                Eval('newJarVersion = '+source.NextJarVersion()+';');
                {cmdCreateTank}
                print tank.Id tankId;

            }}");

            return result;
        }


        [HttpPost("api/liquidity/{kind}/tank/withtank")]
        public async Task<IActionResult> CreateTankWithTankAsync(string kind, [FromBody] CreateTankWithTankBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("kind is required");
            if (body.TankIds == null || body.TankIds.Count == 0) return BadRequest("tankIds is required");
            if(body.TankIds.Count != body.TankIds.Distinct().Count()) return BadRequest("All tank ids in tanks list must be distinct.");
            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty : Validator.StringEscape(body.Description);
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);

            StringBuilder cmdCreateTank = new StringBuilder();
            StringBuilder cmdTankCheck = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            foreach(var tankId in body.TankIds)
            {
                if (tankId <= 0) return BadRequest("All tank ids in tanks list must be greater than zero.");

            }
            foreach (int tankId in body.TankIds)
            {
                cmdTankCheck.AppendLine($@"
                    existTank{tankId} = liquid.Source.ExistTank({tankId});
                    Check(existTank{tankId}) Error 'The request tank id:{tankId} does not exist.';
                    if (existTank{tankId})
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'The tank id:{tankId} is not ready to be added to the new tank.' + tank{tankId}.Type;
                        tankerId = liquid.Source.GetFirstTankerWith({tankId});
                        tankerIsValid = tankerId == -1;
                        Check(tankerIsValid) Error 'The tank id:{tankId} already exists in tanker with id ' + tankerId;

                    }}
                ");
            }

            var tankIds = string.Join(",", body.TankIds);
           
            cmdCreateTank.AppendLine($"tank = source.Jar.CreateTankWithTank(itIsThePresent, Now, tankId, '{escapedName}','{escapedDescription}', {{{tankIds}}});");

            if (!string.IsNullOrWhiteSpace(body.Color))
            {
                cmdCreateTank.AppendLine($"tank.ChangeColor('{body.Color}');");
            }
            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }

                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append($"isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdCreateTank.AppendLine($"tank.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }

            if (body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && !string.IsNullOrEmpty(body.CurrencyCode))
            {
                cmdCreateTank.AppendLine($"tank.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {cmdCheck}   
                    {cmdTankCheck}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('tankId = '+source.NextTankId()+';');
                {cmdCreateTank}
                print tank.Id tankId;
               

            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/merge/tank")]
        public async Task<IActionResult> MergeTankAsync(string kind, [FromBody] MergeTankBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (body.Tanks.Count == 0) return BadRequest("deposits is required");
            if (body.FromTankId == 0) return BadRequest("fromTankId is required");

            if (body.Tanks.Count != body.Tanks.Distinct().Count()) return BadRequest("All tank ids in tanks list must be distinct.");

            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");

            StringBuilder checkTanksQuery = new StringBuilder();
            StringBuilder appendTanksCommand = new StringBuilder();

            bool firstTime = true;
            foreach (int tankId in body.Tanks)
            {
                if (firstTime)
                {
                    firstTime = false;

                    appendTanksCommand.AppendLine($"Eval('newMergeTankId = '+liquid.Source.NextTankId()+';');");
                    appendTanksCommand.AppendLine($"originTank = liquid.Source.FindTank({body.FromTankId});");
                    appendTanksCommand.AppendLine($"tank{tankId} = liquid.Source.FindTank({tankId});");
                    appendTanksCommand.AppendLine($"mergedTank = originTank.MergeWith(itIsThePresent, newMergeTankId, Now, tank{tankId});");
                }
                else
                {
                    appendTanksCommand.AppendLine($"tank{tankId} = liquid.Source.FindTank({tankId});");
                    appendTanksCommand.AppendLine($"mergedTank.MergeWith(tank{tankId});");
                }
                checkTanksQuery.AppendLine($@"
                    exitsTank{tankId} = liquid.Source.ExistTank({tankId});
                    Check(exitsTank{tankId}) Error 'Tank id: {tankId} does not exist.';
                    if (exitsTank{tankId})
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'Tank id: {tankId} is not ready to merge.' + tank{tankId}.Type;

                        tankerId = liquid.Source.GetFirstTankerWith({tankId});
                        tankerIsValid = tankerId == -1;
                        Check(tankerIsValid) Error 'Tank id: {tankId} already exists in tanker with id ' + tankerId;
                    }}
                ");
            }

            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty : Validator.StringEscape(body.Description);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existOriginInTanks = {body.Tanks.Contains(body.FromTankId)};
                Check(!existOriginInTanks) Error 'The origin tank id {body.FromTankId} cannot be in tanks list.';

                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    
                    isAValidTank = liquid.Source.ExistTank({body.FromTankId});
                    Check(isAValidTank) Error 'Tank id: {body.FromTankId} does not exist.';
                    if (isAValidTank)
                    {{
                        originTank = liquid.Source.FindTank({body.FromTankId});
                        isOriginTankReady = originTank.Type == '{typeof(TankReady).Name}';
                        Check(isOriginTankReady) Error 'Tank id: {body.FromTankId} is not ready to merge.' + originTank.Type;
                        {checkTanksQuery}
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;

                    {appendTanksCommand}
                    mergedTank.Name = '{escapedName}';
                    if ({isDescriptionNullOrEmpty})
                    {{
                        mergedTank.Description = '';
                    }}
                    else
                    {{
                        mergedTank.Description = '{escapedDescription}';
                    }}
                    print mergedTank.Id mergedTankId;
                    print mergedTank.Name name;
                }}

            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tank/{tankId}/name")]
        public async Task<IActionResult> UpdateTankNameAsync(string kind, int tankId, [FromBody] UpdateNameBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");

            string escapedName = Validator.StringEscape(body.Name);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    for (sources : liquid.Sources)
                    {{
                        source = sources;
                        existTank = source.ExistTank({tankId});
                        Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;
                    tank = source.FindTank({tankId});
                    tank.UpdateName(itIsThePresent, now, '{escapedName}');
                }}
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tank/{tankId}/description")]
        public async Task<IActionResult> UpdateTankDescriptionAsync(string kind, int tankId, [FromBody] UpdateDescriptionBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");

            string escapedDescription = string.IsNullOrEmpty(body.Description) ? string.Empty : Validator.StringEscape(body.Description);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    for (sources : liquid.Sources)
                    {{
                        source = sources;
                        existTank = source.ExistTank({tankId});
                        Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;
                    tank = source.FindTank({tankId});
                    tank.UpdateDescription(itIsThePresent, now, '{escapedDescription}');
                }}
            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/tankerWithTank")]
        public async Task<IActionResult> CreateTankerAsync(string kind, [FromBody] CreateTankerBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (body.Tanks ==null) return BadRequest("tanks is required");
            if (body.Tanks.Count == 0) return BadRequest("tanks is required");
            if (body.Tanks.Count == 0) return BadRequest("tanks is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");

            var tankIds = string.Join(",", body.Tanks);
            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty :  Validator.StringEscape(body.Description);
            bool isColorNullOrWitheSpace = string.IsNullOrWhiteSpace(body.Color);
            string escapedColor = isColorNullOrWitheSpace ? string.Empty : Validator.StringEscape(body.Color);
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);

            StringBuilder checkTanks = new StringBuilder();
            StringBuilder cmdPropsTanker = new StringBuilder();
            if (body.Tanks != null && body.Tanks.Count > 0)
            {

                foreach (int tankId in body.Tanks)
                {
                    checkTanks.AppendLine($@"
                    exitsTank{tankId} = liquid.Source.ExistTank({tankId});
                    Check(exitsTank{tankId}) Error 'Tank id: {tankId} does not exist.';
                    if (exitsTank{tankId})
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankerReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankerReady) Error 'Tank id: {tankId} is not ready to create a tanker. status is ' + tank{tankId}.Type;

                        tankerId = liquid.Source.GetFirstTankerWith({tankId});
                        tankerIsValid = tankerId == -1;
                        Check(tankerIsValid) Error 'Tank id: {tankId} already exists in tanker with id ' + tankerId;

                        inUseForAnyTanker = liquid.Source.IsTankInUse(tank{tankId});
                        Check(!inUseForAnyTanker) Error 'The request to tank id:{tankId} is in use by a tanker';
                    }}
                ");
                }
            }

            if (!isColorNullOrWitheSpace)
            {
                cmdPropsTanker.AppendLine($@"tanker.ChangeColor('{escapedColor}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }
                if (targetDate != DateTime.MinValue)
                {
                    cmdPropsTanker.AppendLine($"tanker.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }

            if (body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && !string.IsNullOrEmpty(body.CurrencyCode))
            {
                cmdPropsTanker.AppendLine($"tanker.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {checkTanks}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('tankerId = '+source.NextTankerId()+';');
                    
                if ({isDescriptionNullOrEmpty})
                {{
                    tanker = source.CreateTanker(itIsThePresent, Now, tankerId, '{escapedName}', '', {{{tankIds}}});
                    {cmdPropsTanker}
                    print tanker.Id tankerId;
                    print tanker.Name name;
                    print tanker.Description description;
                    print tanker.ContainerColor color;
                }}
                else
                {{
                    tanker = source.CreateTanker(itIsThePresent, Now, tankerId, '{escapedName}', '{escapedDescription}', {{{tankIds}}});
                    {cmdPropsTanker}
                    print tanker.Id tankerId;
                    print tanker.Name name;
                    print tanker.Description description;
                    print tanker.ContainerColor color;
                }}
                 
            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/tanker")]
        public async Task<IActionResult> CreateTankerEmptyAsync(string kind, [FromBody] CreateTankerBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");

            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty : Validator.StringEscape(body.Description);
            bool isColorNullOrWitheSpace = string.IsNullOrWhiteSpace(body.Color);
            string escapedColor = isColorNullOrWitheSpace ? string.Empty : Validator.StringEscape(body.Color);
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);
            StringBuilder cmdPropsTanker = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            if (!isColorNullOrWitheSpace)
            {
                cmdPropsTanker.AppendLine($@"tanker.ChangeColor('{escapedColor}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }
                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append($"isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdPropsTanker.AppendLine($"tanker.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }

            if(body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && !string.IsNullOrEmpty(body.CurrencyCode))
            {
                cmdPropsTanker.AppendLine($"tanker.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {cmdCheck}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('tankerId = '+source.NextTankerId()+';');
                    
                if ({isDescriptionNullOrEmpty})
                {{
                    tanker = source.CreateTanker(itIsThePresent, Now, tankerId, '{escapedName}', '');
                    {cmdPropsTanker}
                    print tanker.Id tankerId;
                    print tanker.Name name;
                    print tanker.Description description;
                    print tanker.ContainerColor color;
                }}
                else
                {{
                    tanker = source.CreateTanker(itIsThePresent, Now, tankerId, '{escapedName}', '{escapedDescription}');
                    {cmdPropsTanker}
                    print tanker.Id tankerId;
                    print tanker.Name name;
                    print tanker.Description description;
                    print tanker.ContainerColor color;
                }}
                 
            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/tanker/fill")]
        public async Task<IActionResult> CreateTankerWithDepositsAsync(string kind, [FromBody] CreateTankerWithDepositBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");
            if (body.DepositIds == null || body.DepositIds.Count == 0) return BadRequest("deposits is required");

            string escapedName = Validator.StringEscape(body.Name);
            bool isDescriptionNullOrEmpty = string.IsNullOrEmpty(body.Description);
            string escapedDescription = isDescriptionNullOrEmpty ? string.Empty : Validator.StringEscape(body.Description);
            bool isColorNullOrWitheSpace = string.IsNullOrWhiteSpace(body.Color);
            string escapedColor = isColorNullOrWitheSpace ? string.Empty : Validator.StringEscape(body.Color);
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);
            foreach (var depositId in body.DepositIds)
            {
                if (depositId <= 0) return BadRequest($"depositId can not be {depositId}");
            }

            StringBuilder cmdPropsTanker = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            StringBuilder cmdDepositsCheck = new StringBuilder();
            StringBuilder cmdCreateTanker = new StringBuilder();
            var depositIds = string.Join(",", body.DepositIds);

            if (!isColorNullOrWitheSpace)
            {
                cmdPropsTanker.AppendLine($@"tanker.ChangeColor('{escapedColor}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }
                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append($"isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                       $"\r\n Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdPropsTanker.AppendLine($"tanker.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }

            if(body.GoalAmount.HasValue && body.GoalAmount.Value > 0 && !string.IsNullOrEmpty(body.CurrencyCode))
            {
                cmdPropsTanker.AppendLine($"tanker.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');");
            }

            foreach ( int depositId in body.DepositIds)
            {
                cmdDepositsCheck.AppendLine($@"
                    existsDeposit{depositId} = liquid.Source.Jar.ExistDepositConfirmed({depositId});
                    Check(existsDeposit{depositId}) Error 'The request deposit id:{depositId} does not exist or is not confirmed.';
                ");
            }

            if (isDescriptionNullOrEmpty)
            {
                cmdCreateTanker.Append($"tanker = source.CreateTankerWithDeposits(itIsThePresent, Now,jarVersion, tankerId, '{escapedName}', '',{{{depositIds}}});");
            }
            else
            {
                cmdCreateTanker.Append($"tanker = source.CreateTankerWithDeposits(itIsThePresent, Now, jarVersion, tankerId, '{escapedName}', '{escapedDescription}',{{{depositIds}}});");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {cmdCheck}
                    {cmdDepositsCheck}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('jarVersion = '+source.NextJarVersion()+';');
                Eval('tankerId = '+source.NextTankerId()+';');
                {cmdCreateTanker}
                {cmdPropsTanker}
                print tanker.Id tankerId;
                print tanker.Name name;
                print tanker.Description description;
                print tanker.ContainerColor color;
                 
            }}");

            return result;
        }


        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/name")]
        public async Task<IActionResult> UpdateTankerNameAsync(string kind, int tankerId, [FromBody] UpdateNameBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");

            string escapedName = Validator.StringEscape(body.Name);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    for (sources : liquid.Sources)
                    {{
                        source = sources;
                        existTanker = source.ExistTanker({tankerId});
                        Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;
                    tanker = source.FindTanker({tankerId});
                    tanker.UpdateName(itIsThePresent, now, '{escapedName}');
                }}
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/description")]
        public async Task<IActionResult> UpdateTankerDescriptionAsync(string kind, int tankerId, [FromBody] UpdateDescriptionBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");

            string escapedDescription = string.IsNullOrEmpty(body.Description) ? string.Empty : Validator.StringEscape(body.Description);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    for (sources : liquid.Sources)
                    {{
                        source = sources;
                        existTanker = source.ExistTanker({tankerId});
                        Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;
                    tanker = source.FindTanker({tankerId});
                    tanker.UpdateDescription(itIsThePresent, now, '{escapedDescription}');
                }}
            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/tanker/{tankerId}/seal")]
        public async Task<IActionResult> SealTankerAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if (existTanker)
                    {{
                        tanker = liquid.Source.FindTanker({tankerId});

                        validTankerType = tanker.Type != '{typeof(TankerSealed).Name}';
                        Check(validTankerType) Error 'The request tanker id:{tankerId} is already sealed. ' + tanker.Type;

                        validSealedType = tanker.Type == '{typeof(TankerPending).Name}';
                        Check(validSealedType) Error 'The request tanker id:{tankerId} is not a sealed tanker. ' + tanker.Type;
                    }}

                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (sources : liquid.Sources)
                {{
                    source = sources;

                    tankerPending = source.FindTanker({tankerId});
                    tankerSealed = tankerPending.Sealed();

                    print tankerSealed.Id tankerId;
                    print tankerSealed.Name name;
                    print tankerSealed.Type status;
                    print tankerSealed.Description description;

                }}

            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/tanker/{tankerId}/disburden")]
        public async Task<IActionResult> DisburdenTankerAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if (existTanker)
                    {{
                        tankerSealed = liquid.Source.FindTanker({tankerId});
                        validSealedType = tankerSealed.Type == '{typeof(TankerSealed).Name}';
                        Check(validSealedType) Error 'The request tanker id:{tankerId} is not a sealed tanker.' + tankerSealed.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tankerSealed = liquid.Source.FindTanker({tankerId});
                tankerSealed.Disburden(itIsThePresent);
            }}");

            return result;
        }

        [HttpPost("api/liquidity/{kind}/tanker/{tankerId}/undisburden")]
        public async Task<IActionResult> UndisburdenTankerAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required");
            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if (existTanker)
                    {{
                        tankerSealed = liquid.Source.FindTanker({tankerId});
                        validSealedType = tankerSealed.Type == '{typeof(TankerSealed).Name}';
                        Check(validSealedType) Error 'The request tanker id:{tankerId} is not a sealed tanker.' + tankerSealed.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tankerSealed = liquid.Source.FindTanker({tankerId});
                tankerSealed.Undisburden();
            }}");
            return result;
        }

        [HttpGet("api/liquidity/{kind}/dispensers")]
        public async Task<IActionResult> DispensersAsync(string kind, DateTime startDate, DateTime endDate,string status, int fromDispenserId = 0)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (fromDispenserId < 0)
            {
                return BadRequest("fromDispenserId must be greater than or equal to zero.");
            }
            FilterContainerStatus newDispenserStatus;
            if (string.IsNullOrEmpty(status))
            {
                newDispenserStatus = FilterContainerStatus.ALL;
            }
            else if (!Enum.TryParse<FilterContainerStatus>(status, true, out newDispenserStatus))
            {
                return BadRequest("Invalid dispenser status value.");
            }
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            StringBuilder cmdFilterDispenser = new StringBuilder();
            StringBuilder qryFilterTank = new StringBuilder();
            if (startDate != DateTime.MinValue && endDate != DateTime.MinValue)
            {
                if (startDate > endDate)
                    return BadRequest("startDate must be less than or equal to endDate");

                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

                cmdFilterDispenser.AppendLine($"allDispensers = liquid.Outlet.BuildDispenserWithWithdrawalsBetween({formattedStartDate}, {formattedEndDate}, {newDispenserStatus});");
            }
            else if (startDate != DateTime.MinValue)
            {
                string formattedStartDate = startDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterDispenser.AppendLine($"allDispensers = liquid.Outlet.BuildDispenserWithWithdrawalsFrom({formattedStartDate},{newDispenserStatus});");
            }
            else if (endDate != DateTime.MinValue)
            {
                string formattedEndDate = endDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                cmdFilterDispenser.AppendLine($"allDispensers = liquid.Outlet.BuildDispenserWithWithdrawalsUpTo({formattedEndDate},{newDispenserStatus});");
            }
            else if (fromDispenserId == 0)
            {
                cmdFilterDispenser.AppendLine($"allDispensers = liquid.Outlet.RootHierarchyDispensers({newDispenserStatus});");
            }
            else if (fromDispenserId > 0)
            {
                qryFilterTank.AppendLine($@"
                    if(existKind)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                        existDispenser = liquid.Outlet.ExistDispenser({fromDispenserId});
                        Check(existDispenser) Error 'The request dispenser id:{fromDispenserId} does not exist.';
                    }}
                ");
                cmdFilterDispenser.AppendLine($@"dispenser = liquid.Outlet.FindDispenser({fromDispenserId});");
                cmdFilterDispenser.AppendLine("allDispensers = dispenser.DescendantDispensers(true);");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                {qryFilterTank}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                print liquid.Outlet.MaxDateDispenser maxDate;
                print liquid.Outlet.MinDateDispenser minDate;
                {cmdFilterDispenser}
                for (dispensers : allDispensers)
                {{
                    dispenser = dispensers;
                    print dispenser.Id id;
                    print dispenser.Name name;
                    print dispenser.Description description;
                    print dispenser.Amount amount;
                    print dispenser.ReceivedAmount receivedAmount;
                    print dispenser.Type status;
                    print dispenser.ContainerColor color;
                    print dispenser.GoalAmount.GetAmount() goalAmount;
                    print dispenser.TargetDate targetDate;
                    print dispenser.Path path;
                    for (parentPaths : dispenser.ParentChain)
                    {{
                        path = parentPaths;
                        print path.Id id;
                        print path.Name name;
                    }}  
                    for (withdrawals : dispenser.ExplandedWithdrawals)
                    {{
                        withdrawal = withdrawals;
                        print withdrawal.Id withdrawalId;
                        print withdrawal.Amount amount;
                        print withdrawal.CreatedAt createdDate;
                        print withdrawal.Reference reference;
                        print withdrawal.ExternalReference externalReference;
                        print withdrawal.Destination destination;
                        print withdrawal.AtAddress atAddress;
                        print withdrawal.ValueDirectionAsString({resultRate.Value}) valueDirection;
                        print withdrawal.Rate rate;
                        
                        inDispenser = liquid.Outlet.FindDispenserFromWithdrawal(withdrawal);
                        enclosureWithdrawal = inDispenser.FindEnclosureWithdrawal(withdrawal);
                        print enclosureWithdrawal.Notes notes;
                        print enclosureWithdrawal.ClaimedVersion claimedVersion;
                        print enclosureWithdrawal.ClaimedDate claimedDate;
                        print enclosureWithdrawal.CanceledDated canceledDated;
                    }}
                }}
            }}");

            return result;
        }


        [HttpGet("api/liquidity/{kind}/dispenser/{dispenserId}/detail")]
        public async Task<IActionResult> DispensersDetailAsync(string kind,int dispenserId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existkind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser id:{dispenserId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});
                print dispenser.Id id;
                print dispenser.Name name;
                print dispenser.Description description;
                print dispenser.Amount amount;
                print dispenser.ReceivedAmount receivedAmount;
                print dispenser.Type status;
                print dispenser.ContainerColor color;
                print dispenser.TargetDate targetDate;
                print dispenser.GoalAmount.GetAmount() goalAmount;
                print dispenser.Path path;
                for (parentPaths : dispenser.ParentChain)
                {{
                    path = parentPaths;
                    print path.Id id;
                    print path.Name name;
                }}  
                for (withdrawals : dispenser.Withdrawals)
                {{
                    withdrawal = withdrawals;
                    print withdrawal.Id withdrawalId;
                    print withdrawal.Amount amount;
                    print withdrawal.CreatedAt createdDate;
                    print withdrawal.Reference reference;
                    print withdrawal.ExternalReference externalReference;
                    print withdrawal.Destination destination;
                    print withdrawal.AtAddress atAddress;
                    print withdrawal.ValueDirectionAsString({resultRate.Value}) valueDirection;
                    print withdrawal.ExchangeVariation({resultRate.Value}) exchangeRateVariation;
                    print 0.0 fee;
                    inDispenser = liquid.Outlet.FindDispenserFromWithdrawal(withdrawal);
                    enclosureWithdrawal = inDispenser.FindEnclosureWithdrawal(withdrawal);
                    print enclosureWithdrawal.Notes notes;
                }}
                for (flattenedDispensers : dispenser.FlattenedList)
                {{
                    dispenser = flattenedDispensers;
                    print dispenser.Id id;
                    print dispenser.Name name;
                    print dispenser.Description description;    
                    print dispenser.Amount amount;
                    print dispenser.ReceivedAmount receivedAmount;
                    print dispenser.ContainerColor color;
                    print dispenser.Path path;

                }}
            }}");

            return result;
        }


        [HttpPut("api/liquidity/{kind}/dispenser/{dispenserId}/update/color")]
        public async Task<IActionResult> ChangeColorDispenserAsync(string kind, int dispenserId, [FromBody] BodydColor body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("Body is required");
            if (body.NewColor == null) return BadRequest("Body NewColor is required");
            
            string scapedColor = !string.IsNullOrEmpty(body.NewColor) ? Validator.StringEscape(body.NewColor) : "";

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existkind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser id:{dispenserId} does not exist.';
                    if(existDispenser)
                    {{
                       liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                       dispenser = liquid.Outlet.FindDispenser({dispenserId});
                       isSameColor = dispenser.ItsSameColor('{scapedColor}');
                       Check(!isSameColor) Error 'The new color must be different from the old color.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});
                dispenser.ChangeColor('{scapedColor}');
            }}");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/payouts")]
        public async Task<IActionResult> DispenserInboxAsync([FromRoute] string kind, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("The 'kind' parameter is required.");
            if (startDate.HasValue && endDate.HasValue && startDate > endDate)
            {
                return BadRequest("startDate must be less than or equal to endDate");
            }
            if (startDate.HasValue && startDate.Value.TimeOfDay != TimeSpan.Zero)
            {
                return BadRequest("The startDate parameter must not include a time component (hours, minutes, or seconds). Please provide the date only (e.g., 'MM/dd/yyyy').");
            }

            if (endDate.HasValue && endDate.Value.TimeOfDay != TimeSpan.Zero)
            {
                return BadRequest("The endDate parameter must not include a time component (hours, minutes, or seconds). Please provide the date only (e.g., 'MM/dd/yyyy').");
            }

            DateTime effectiveStartDate = startDate ?? new DateTime(1951, 1, 1);
            DateTime effectiveEndDate = endDate ?? new DateTime(2049, 12, 31);

            string formattedStartDate = effectiveStartDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
            string formattedEndDate = effectiveEndDate.ToString("MM/dd/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(ItIsThePresent, Now, '{kind}');
                inbox = liquid.Outlet.DispenserInbox;

                filteredWithdrawals = inbox.WithdrawalsBy({formattedStartDate}, {formattedEndDate});

                Print inbox.Id 'id';
                Print inbox.Name 'name';
                Print inbox.Description 'description';
                Print inbox.Amount amount;
                Print inbox.Type 'status';
        
                for (withdrawals : filteredWithdrawals)
                {{
                    withdrawal = withdrawals;
                    Print withdrawal.Id 'id';
                    Print withdrawal.PullPaymentId 'pullPaymentId';
                    Print withdrawal.Amount 'amount';
                    Print withdrawal.receivedAmount 'receivedAmount';
                    Print withdrawal.Destination 'destination';
                    Print withdrawal.AtAddress 'atAddress';
                    Print withdrawal.CreatedAt 'createdDate';
                    Print withdrawal.ExternalReference 'externalReference';
                    Print withdrawal.StoreId 'storeId';
                }}
            }}
            ");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/dispensers/withdrawals")]
        public async Task<IActionResult> DispensersAsync([FromRoute] string kind, [FromQuery] string? name)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("The 'kind' parameter is required.");

            var nameFilterEscaped = string.IsNullOrEmpty(name) ? string.Empty : Validator.StringEscape(name);
            
            var result = await LiquidityAPI.Liquidity.PerformQryAsync(HttpContext, $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(ItIsThePresent, Now, '{kind}');
                filteredDispensers = liquid.Outlet.FindDispensersBy('{nameFilterEscaped}');
        
                for (dispensers : filteredDispensers)
                {{
                    dispenser = dispensers;
                    Print dispenser.Id 'id';
                    Print dispenser.Name 'name';
                    Print dispenser.Description 'description';
                    Print dispenser.Type 'type';
                    Print dispenser.Amount 'amount';
                    print dispenser.ContainerColor 'color';

                    for (withdrawals : dispenser.ExplandedWithdrawals)
                    {{
                        withdrawal = withdrawals;
                        Print withdrawal.Id 'id';
                        Print withdrawal.PullPaymentId 'pullPaymentId';
                        Print withdrawal.Amount 'amount';
                        Print withdrawal.Destination 'destination';
                        Print withdrawal.AtAddress 'atAddress';
                        Print withdrawal.CreatedAt 'createdDate';
                        Print withdrawal.ExternalReference 'externalReference';
                        Print withdrawal.StoreId 'storeId';
                        
                        inDispenser = liquid.Outlet.FindDispenserFromWithdrawal(withdrawal);
                        enclosureWithdrawal = inDispenser.FindEnclosureWithdrawal(withdrawal);
                        print enclosureWithdrawal.Notes notes;
                        print enclosureWithdrawal.ClaimedVersion claimedVersion;
                        print enclosureWithdrawal.ClaimedDate claimedDate;
                        print enclosureWithdrawal.CanceledDated canceledDated;
                    }}
                }}
            }}
            ");

            return result;
        }

        [DataContract(Name = "DispenserBodyWithDeposits")]
        public class DispenserBodyWithDeposits: DispenserBody
        {
            [DataMember(Name = "withdrawalIds")]
            public List<int> WithdrawalIds { get; set; }

        }

        public class DispenserBodyWithDispenser : DispenserBody
        {
            [DataMember(Name = "DispenserIds")]
            public List<int> DispenserIds { get; set; }

        }

        [DataContract(Name = "DispenserBody")]
        public class DispenserBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }

            [DataMember(Name = "color")]
            public string? Color { get; set; }
            [DataMember(Name = "targetDate")]
            public string? TargetDate { get; set; }
            public decimal? GoalAmount { get; set; }

        }

        [HttpPost("api/liquidity/{kind}/dispenser")]
        public async Task<IActionResult> CreateDispenserAsync(string kind, [FromBody] DispenserBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");
            if (string.IsNullOrWhiteSpace(body.Description)) body.Description = string.Empty;

            string escapedName = Validator.StringEscape(body.Name);
            string escapedDescription = Validator.StringEscape(body.Description);
            string scapedColor = !string.IsNullOrEmpty(body.Color) ? Validator.StringEscape(body.Color):"";
            bool hasColor = scapedColor.Trim().Length > 0;
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);
            StringBuilder cmdProps = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            StringBuilder cmdCreateDispenser = new StringBuilder();
            if (hasColor)
            {
                cmdProps.AppendLine($"dispenser.ChangeColor('{scapedColor}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }

                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append($"liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');" +
                        $"\r\n      isValidStartDate =liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n      Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdCreateDispenser.AppendLine($"dispenser = liquid.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, '{escapedName}', '{escapedDescription}', {targetDate.ToString("M/d/yyyy H:m:s")});");
                }
            }
            else
            {
                cmdCreateDispenser.AppendLine($"dispenser = liquid.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, '{escapedName}', '{escapedDescription}');");
            }

            if (body.GoalAmount.HasValue && body.GoalAmount.Value > 0)
            {
                cmdProps.AppendLine($"dispenser.AddGoalAmount({body.GoalAmount},'{kind}');");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    {cmdCheck}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                Eval('dispenserId = '+liquid.Outlet.NextDispenserId()+';');
                {cmdCreateDispenser}
                {cmdProps}
                print dispenser.Id dispenserId;
            }}");
            return result;
        }


        [HttpPost("api/liquidity/{kind}/dispenser/withdrawal")]
        public async Task<IActionResult> CreateDispenserWithWithdrawalsAsync(string kind, [FromBody] DispenserBodyWithDeposits body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");
            if (string.IsNullOrWhiteSpace(body.Description)) body.Description = string.Empty;
            if(body.WithdrawalIds == null || body.WithdrawalIds.Count == 0) return BadRequest("withdrawalIds is required");
            foreach (var withdrawalId in body.WithdrawalIds)
            {
                if (withdrawalId <= 0) return BadRequest($"withdrawalId can not be {withdrawalId}");
            }
            if (body.WithdrawalIds.Count != body.WithdrawalIds.Distinct().Count())
            {
                return BadRequest("All withdrawalIds in the list must be distinct.");
            }

            string escapedName = Validator.StringEscape(body.Name);
            string escapedDescription = Validator.StringEscape(body.Description);
            string scapedColor = !string.IsNullOrEmpty(body.Color) ? Validator.StringEscape(body.Color) : "";
            bool hasColor = scapedColor.Trim().Length > 0;
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);

            StringBuilder checkWithdrawal = new StringBuilder();
            foreach (int withdrawalId in body.WithdrawalIds)
            {
                checkWithdrawal.AppendLine($@"
                    existWithdrawal{withdrawalId} = liquid.Outlet.DispenserInbox.HasWithdrawal({withdrawalId});
                    Check(existWithdrawal{withdrawalId}) Error 'Withdrawal id: {withdrawalId} does not exist.';
                    if (existWithdrawal{withdrawalId})
                    {{
                        withdrawal{withdrawalId} = liquid.Outlet.DispenserInbox.FindEnclosureWithdrawal({withdrawalId});
                        isClaimed = withdrawal{withdrawalId}.IsClaimed;
                        Check(!isClaimed) Error 'Withdrawal id: {withdrawalId} is not ready to add dispenser';
                    }}
                ");
            }

            StringBuilder cmdProps = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            StringBuilder cmdCreateDispenser = new StringBuilder();
            var withdrawalIds = string.Join(",", body.WithdrawalIds);
            if (hasColor)
            {
                cmdProps.AppendLine($"dispenser.ChangeColor('{scapedColor}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }

                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append(
                        $"\r\n      isValidStartDate =liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n      Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdCreateDispenser.AppendLine($"dispenser = liquid.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, '{escapedName}', '{escapedDescription}', {targetDate.ToString("M/d/yyyy H:m:s")}, {{{withdrawalIds}}});");
                }
            }
            else
            {
                cmdCreateDispenser.AppendLine($"dispenser = liquid.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, '{escapedName}', '{escapedDescription}', {{{withdrawalIds}}});");
            }

            if (body.GoalAmount.HasValue && body.GoalAmount.Value > 0)
            {
                cmdProps.AppendLine($"dispenser.AddGoalAmount({body.GoalAmount},'{kind}');");
            }
            
            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {cmdCheck}
                    {checkWithdrawal}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                Eval('dispenserId = '+liquid.Outlet.NextDispenserId()+';');
                {cmdCreateDispenser}
                {cmdProps}
                print dispenser.Id dispenserId;
            }}");
            return result;
        }

        [HttpPost("api/liquidity/{kind}/dispenser/withDispenser")]
        public async Task<IActionResult> CreateDispenserWithDispenserAsync(string kind, [FromBody] DispenserBodyWithDispenser body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");
            if (string.IsNullOrWhiteSpace(body.Description)) body.Description = string.Empty;
            if (body.DispenserIds == null || body.DispenserIds.Count == 0) return BadRequest("withdrawalIds is required");
            foreach (var withdrawalId in body.DispenserIds)
            {
                if (withdrawalId <= 0) return BadRequest($"withdrawalId can not be {withdrawalId}");
            }
            if (body.DispenserIds.Count != body.DispenserIds.Distinct().Count())
            {
                return BadRequest("All withdrawalIds in the list must be distinct.");
            }

            string escapedName = Validator.StringEscape(body.Name);
            string escapedDescription = Validator.StringEscape(body.Description);
            string scapedColor = !string.IsNullOrEmpty(body.Color) ? Validator.StringEscape(body.Color) : "";
            bool hasColor = scapedColor.Trim().Length > 0;
            bool hasTargetDate = !string.IsNullOrEmpty(body.TargetDate);

            StringBuilder checkWithdrawal = new StringBuilder();
            foreach (int dispenserId in body.DispenserIds)
            {
                checkWithdrawal.AppendLine($@"
                    existDispenser{dispenserId} = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser{dispenserId}) Error 'Withdrawal id: {dispenserId} does not exist.';
                    if (existDispenser{dispenserId})
                    {{
                        dispenser{dispenserId} = liquid.Outlet.FindDispenser({dispenserId});
                        inUseForAnyDispenser = liquid.Outlet.IsDispenserInUse(dispenser{dispenserId});
                        Check(!inUseForAnyDispenser) Error 'The request to dispenser id:{dispenserId} is in use by a dispenser that has been committed.';
                    }}
                ");
            }

            StringBuilder cmdProps = new StringBuilder();
            StringBuilder cmdCheck = new StringBuilder();
            StringBuilder cmdCreateDispenser = new StringBuilder();
            var dispenserIds = string.Join(",", body.DispenserIds);
            if (hasColor)
            {
                cmdProps.AppendLine($"dispenser.ChangeColor('{scapedColor}');");
            }

            if (hasTargetDate)
            {
                if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
                {
                    return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
                }

                if (targetDate != DateTime.MinValue)
                {
                    cmdCheck.Append(
                        $"\r\n      isValidStartDate =liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});" +
                        $"\r\n      Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';");
                    cmdCreateDispenser.AppendLine($"dispenser = liquid.Outlet.CreateDispenserWithDispenser(itIsThePresent, now, dispenserId, '{escapedName}', '{escapedDescription}', {targetDate.ToString("M/d/yyyy H:m:s")}, {{{dispenserIds}}});");
                }
            }
            else
            {
                cmdCreateDispenser.AppendLine($"dispenser = liquid.Outlet.CreateDispenserWithDispenser(itIsThePresent, now, dispenserId, '{escapedName}', '{escapedDescription}', {{{dispenserIds}}});");
            }

            if (body.GoalAmount.HasValue && body.GoalAmount.Value > 0)
            {
                cmdProps.AppendLine($"dispenser.AddGoalAmount({body.GoalAmount},'{kind}');");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    {cmdCheck}
                    {checkWithdrawal}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                Eval('dispenserId = '+liquid.Outlet.NextDispenserId()+';');
                {cmdCreateDispenser}
                {cmdProps}
                print dispenser.Id dispenserId;
            }}");
            return result;
        }

        [DataContract(Name = "WithdrawBody")]
        public class WithdrawBody
        {
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "amountCurrency")]
            public string CurrencyAmount { get; set; } = $"{Currencies.CODES.USD}";
            [DataMember(Name = "destination")]
            public string Destination { get; set; }
            [DataMember(Name = "atAddress")]
            public string AtAddress { get; set; }
            [DataMember(Name = "externalReference")]
            public string ExternalReference { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [HttpPost("api/liquidity/{kind}/dispenser/withdraw")]
        public async Task<IActionResult> CreateWithdrawAsync(string kind, [FromBody] WithdrawBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("dispenserBody is required");
            if (body.Amount <= 0) return BadRequest("amount must be greater than zero");
            //if (string.IsNullOrWhiteSpace(body.CurrencyAmount)) return BadRequest("CurrencyAmount is required");
            if (string.IsNullOrWhiteSpace(body.Destination)) return BadRequest("destination is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest("atAddress is required");
            if (string.IsNullOrWhiteSpace(body.ExternalReference)) return BadRequest("externalReference is required");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest("domain-url header is required");
            if (string.IsNullOrWhiteSpace(body.EmployeeName)) return BadRequest("employeeName is required");
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");

            var nextWithdrawQuery = await RetrieveNextWithdrawAsync(kind, domain, resultRate.Value, body.Amount);
            if (!(nextWithdrawQuery is OkObjectResult)) return nextWithdrawQuery;

            OkObjectResult o = (OkObjectResult)nextWithdrawQuery;
            string json = o.Value.ToString();
            var nexWithdrawData = JsonConvert.DeserializeObject<NextWithdrawDTO>(json);
            if (nexWithdrawData == null) return BadRequest("Failed to retrieve deposit ID.");

            decimal exchangeAmount = decimal.Parse(nexWithdrawData.ExchangeAmount.ToString("F8"), System.Globalization.CultureInfo.InvariantCulture);
            int authorizationLock = await PaymentChannels.LockBalanceAsync(
                atAddress: body.AtAddress,
                purchaseTotal: exchangeAmount,
                currency: kind,
                concept: "Withdraw from dispenser",
                referenceNumber: body.ExternalReference,
                accountNumber: kind,
                employeeName: body.EmployeeName,
                storeId: nexWithdrawData.StoreId,
                useless: DateTime.Now,
                agentId: nexWithdrawData.AgentId,
                domainUrl: nexWithdrawData.DomainUrl,
                domainId: nexWithdrawData.DomainId,
                paymentMethodType: PaymentMethod.ThirdParty,
                entityId: 4 // Fiero
            );
            if (authorizationLock == ASITenantDriver.FAKE_TICKET_NUMBER)
            {
                return BadRequest("Failed to lock balance for withdrawal or insufficient funds.");
            }

            //var pullPayment = await PaymentManager.CreatePullPaymentAsync($"Withdraw", body.Amount, kind, body.CurrencyAmount);
            var pullPayment = await PaymentManager.CreatePullPaymentAsync($"Withdraw", body.Amount, kind);
            if (pullPayment == null) return BadRequest("Failed to create pull payment for withdrawal.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';                
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                Eval('withdrawalId = '+liquid.Outlet.NextWithdrawalId()+';');
                domain = company.Sales.DomainFrom('{domain}');
                withdrawal = liquid.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, '{pullPayment.Id}', {authorizationLock}, {exchangeAmount}, '{body.Destination}', '{body.AtAddress}', domain, {nexWithdrawData.StoreId}, '{body.ExternalReference}',{resultRate.Value}, {body.Amount});
                print withdrawal.Id withdrawalId;
            }}");
            if (!(result is OkObjectResult okResult)) return result;

            return result;
        }

        [HttpPut("api/liquidity/{kind}/dispenser/withdraw/{withdrawalId}/cancel")]
        public async Task<IActionResult> CancelWithdrawAsync(string kind, int withdrawalId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if(withdrawalId <= 0) return BadRequest("withdrawalId is required and must be greater than zero.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';                
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                liquid.Outlet.CancelWithdrawal(itIsThePresent, now, {withdrawalId}); 
            }}");

            if(result is not OkObjectResult)
            {
                return result;
            }

            return Ok(1);
        }

        [HttpPut("api/liquidity/{kind}/dispenser/withdraw/{withdrawalId}/reschedule")]
        public async Task<IActionResult> RescheduleWithdrawAsync(string kind, int withdrawalId)
        {

            return Ok(1);
        }


        public class MoveWithdrawBody
        {
            [DataMember(Name = "fromDispenserId")]
            public int FromDispenserId { get; set; }
            [DataMember(Name = "toDispenserId")]
            public int ToDispenserId { get; set; }
            [DataMember(Name = "withdrawId")]
            public int WithdrawId { get; set; }
        }

        public class MoveDepositBody
        {
            [DataMember(Name = "fromTankId")]
            public int FromTankId { get; set; }
            [DataMember(Name = "toTankId")]
            public int ToTankId { get; set; }
            [DataMember(Name = "depositId")]
            public int DepositId { get; set; }
        }

        [HttpPost("api/liquidity/{kind}/withdraw/assignDispenser")]
        public async Task<IActionResult> MoveWithdrawDispenserAsync(string kind, [FromBody] MoveWithdrawBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (body.FromDispenserId <= 0) return BadRequest("fromDispenserId is required and must be greater than zero.");
            if (body.ToDispenserId <= 0) return BadRequest("toDispenserId is required and must be greater than zero.");
            if (body.WithdrawId <= 0) return BadRequest("withdrawId is required and must be greater than zero.");
            if(body.FromDispenserId == body.ToDispenserId) return BadRequest("fromDispenserId and toDispenserId cannot be the same.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existFromDispenser = liquid.Outlet.ExistDispenser({body.FromDispenserId});
                    Check(existFromDispenser) Error 'The request fromDispenser id:{body.FromDispenserId} does not exist.';
                    
                    existToDispenser = liquid.Outlet.ExistDispenser({body.ToDispenserId});
                    Check(existToDispenser) Error 'The request toDispenser id:{body.ToDispenserId} does not exist.';

                    if (existFromDispenser && existToDispenser)
                    {{
                        fromDispenser = liquid.Outlet.FindDispenser({body.FromDispenserId});
                        isDispenserReady = fromDispenser.Type == '{typeof(DispenserReady).Name}';
                        isDispenserInBox = fromDispenser.Type == '{typeof(DispenserInbox).Name}';
                        if(isDispenserReady)
                        {{
                             hasBeenCommitted = fromDispenser.HasBeenCommitted;
                             Check(!hasBeenCommitted) Error 'The request dispenser id:{body.FromDispenserId} has already been committed.';
                        }}    
                        isAnValidDispenserToMove = isDispenserReady || isDispenserInBox;
                        Check(isAnValidDispenserToMove) Error 'The request fromDispenser id:{body.FromDispenserId} is not ready to receive withdrawals.' + fromDispenser.Type;
                        toDispenser = liquid.Outlet.FindDispenser({body.ToDispenserId});
                        isDispenserReady = toDispenser.Type == '{typeof(DispenserReady).Name}';
                        if(isDispenserReady)
                        {{
                             hasBeenCommitted = toDispenser.HasBeenCommitted;
                             Check(!hasBeenCommitted) Error 'The request dispenser id:{body.ToDispenserId} has already been committed.';
                        }}
                        isDispenserInBox = toDispenser.Type == '{typeof(DispenserInbox).Name}';
                        isAnValidDispenserToMove = isDispenserReady || isDispenserInBox;

                        Check(isAnValidDispenserToMove) Error 'The request toDispenser id:{body.ToDispenserId} is not ready to receive withdrawals.' + toDispenser.Type;    
                        
                        existWithdraw = liquid.Outlet.ExistWithdrawal({body.WithdrawId});
                        Check(existWithdraw) Error 'The request withdraw id:{body.WithdrawId} does not exist in the from dispenser.';
                        existWithdrawInDispenser = fromDispenser.HasWithdrawal({body.WithdrawId});
                        Check(existWithdrawInDispenser) Error 'The request withdraw id:{body.WithdrawId} does not exist in the from dispenser.';
                        existWithdrawInToDispenser = toDispenser.HasWithdrawal({body.WithdrawId});
                        Check(!existWithdrawInToDispenser) Error 'The request withdraw id:{body.WithdrawId} already exists in the to dispenser.';
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                fromDispenser = liquid.Outlet.FindDispenser({body.FromDispenserId});
                toDispenser = liquid.Outlet.FindDispenser({body.ToDispenserId});
                withdrawal = liquid.Outlet.FindWithdrawal({body.WithdrawId});

                fromDispenser.MoveWithdrawal(toDispenser.Id, withdrawal);
            }}");
            return result;
        }

        [HttpPost("api/liquidity/{kind}/deposit/assignTank")]
        public async Task<IActionResult> MoveDepositFromTankToTankAsync(string kind, [FromBody] MoveDepositBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (body == null) return BadRequest("body is required");
            if (body.FromTankId <= 0) return BadRequest("FromTankId is required and must be greater than zero.");
            if (body.ToTankId <= 0) return BadRequest("ToTankId is required and must be greater than zero.");
            if (body.DepositId <= 0) return BadRequest("DepositId is required and must be greater than zero.");
            if (body.FromTankId == body.ToTankId) return BadRequest("FromTankId and ToTankId cannot be the same.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existFromTank = liquid.Source.ExistTank({body.FromTankId});
                    Check(existFromTank) Error 'The request fromTank id:{body.FromTankId} does not exist.';

                    existToTank = liquid.Source.ExistTank({body.ToTankId});
                    Check(existToTank) Error 'The request toTank id:{body.ToTankId} does not exist.';

                    if (existFromTank && existToTank)
                    {{
                        fromTank = liquid.Source.FindTank({body.FromTankId});
                        toTank = liquid.Source.FindTank({body.ToTankId});
                        isValidFromTank = fromTank.Type == '{typeof(TankReady).Name}' || fromTank.Type == '{typeof(TankRoot).Name}';
                        isValidToTank = toTank.Type == '{typeof(TankReady).Name}' || toTank.Type == '{typeof(TankRoot).Name}' ;
                        isAnValidTanksToMove = isValidFromTank && isValidToTank;
                        Check(isAnValidTanksToMove) Error 'The request FromTankId id:{body.FromTankId} or {body.ToTankId} is not ready to receive deposits.';
                        if(isAnValidTanksToMove)
                        {{
                            existDepositInFromTank = fromTank.ExistDeposit({body.DepositId});
                            Check(existDepositInFromTank) Error 'The request deposit id:{body.DepositId} does not exist in the from tank.';
                            existDepositInToTank = toTank.ExistDeposit({body.DepositId});
                            Check(!existDepositInToTank) Error 'The request deposit id:{body.DepositId} already exists in the to tank.';
                        }}
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                fromTank = liquid.Source.FindTank({body.FromTankId});
                toTank = liquid.Source.FindTank({body.ToTankId});
                deposit = fromTank.FindDepositById({body.DepositId});

                fromTank.MoveDepositToTank(toTank.Id, deposit);
            }}");
            return result;
        }

        public class WithdrawalDTO
        {
            public List<WithdrawalDestionationDTO> Withdrawals { get; set; }
            public class WithdrawalDestionationDTO
            {
                public decimal Amount { get; set; }
                public string Destination { get; set; }
            }
        }

        [HttpPost("api/liquidity/{kind}/dispenser/{dispenserId}/commitWithdrawals")]
        public async Task<IActionResult> CommitWithdrawalsAsync(string kind, int dispenserId, [FromHeader(Name = "domain-url")] string domain)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("dispenserId is required and must be greater than zero.");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest("domain-url header is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The request dispenser id:{dispenserId} does not exist.';
                    if (existDispenser)
                    {{
                        dispenser = liquid.Outlet.FindDispenser({dispenserId});
                        isDispenserReady = dispenser.Type == '{typeof(DispenserReady).Name}';
                        Check(isDispenserReady) Error 'The request dispenser id:{dispenserId} is not ready to commit withdrawals.' + dispenser.Type;
                        if (isDispenserReady)
                        {{
                            hasBeenCommitted = dispenser.HasBeenCommitted;
                            Check(!hasBeenCommitted) Error 'The request dispenser id:{dispenserId} has already been committed.';
                        }}
                        
                        validDomain = company.Sales.ExistsDomain('{domain}');
                        Check(validDomain) Error 'The request domainUrl:{domain} does not exist.';
                        if (validDomain)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            checkPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                            Check(checkPaymentDock) Error 'The request domainUrl:{domain} does not have a payment dock associated.';
                        }}
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});

                domain = company.Sales.DomainFrom('{domain}');
                paymentDock = liquid.ParentFlow.FindPaymentEngineDock(domain);
                dispenser.CommitWithdrawals(itIsThePresent, now, paymentDock);
            }}");
            return result;
        }

        [HttpGet("api/liquidity/{kind}/sentinel/tasks")]
        public async Task<IActionResult> SentinelConfirmsAsync(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                for (inboundTasks : liquid.IngressSentinel.InboundTasks)
                {{
                    inboundTask = inboundTasks;
                    print inboundTask.Type taskType;
                    print inboundTask.ToString() taskInformation;

                    if (inboundTask.Type == '{typeof(ConfirmationsInboundTask).Name}')
                    {{
                        print inboundTask.Deposit.Id depositId;
                        print inboundTask.Deposit.InvoiceId InvoiceId;
                        print inboundTask.CurrentConfirmations confirmations;
                        print inboundTask.TotalConfirmations riskConfirmations;
                    }}
                    else if (inboundTask.Type == '{typeof(TankerInboundTask).Name}')
                    {{
                        for (depositStatus : inboundTask.DepositChainStatuses)
                        {{
                            depositState = depositStatus;
                            print depositState.Id id;
                            print depositState.Destination destination;
                            print depositState.Available available;
                            print depositState.Total total;
                        }}
                    }}
                    else if (inboundTask.Type == '{typeof(RateInboundTask).Name}')
                    {{
                        print inboundTask.Kind kind;
                        print inboundTask.LastUpdate lastUpdate;
                        print inboundTask.CurrentRate currentRate;
                    }}
                }}
                for (outboundTasks : liquid.EgressSentinel.OutboundTasks)
                {{
                    outboundTask = outboundTasks;
                    print outboundTask.Type taskType;
                    print outboundTask.ToString() taskInformation;

                    if (outboundTask.Type == '{typeof(ConfimationsOutboundTask).Name}')
                    {{
                        print outboundTask.DispenserId dispenserId;
                        print outboundTask.ConfirmedWithdrawals confirmedWithdrawals;
                        print outboundTask.TotalWithdrawals totalWithdrawals;
                    }}
                }}
            }}");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/rate/{referenceKind}")]
        public async Task<IActionResult> RateAsync(string kind, string referenceKind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (string.IsNullOrWhiteSpace(referenceKind)) return BadRequest("referenceKind is required");
            var result = await PaymentManager.ExchangeRateAsync(kind, referenceKind);
            if (result == null) return NotFound($"Exchange rate from {kind} to {referenceKind} not found.");
            return Ok(new
            {
                From = kind,
                To = referenceKind,
                Rate = result.Rate,
            });
        }

        [HttpGet("api/liquidity/{kind}/current/rate/{referenceKind}")]
        public async Task<IActionResult> CurrentRateAsync(string kind, string referenceKind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (string.IsNullOrWhiteSpace(referenceKind)) return BadRequest("referenceKind is required");


            var query = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
            }}
            ", $@"
            {{
                print Now now;
            }}
            ");

            if (!(query is OkObjectResult)) return query;
            OkObjectResult o = (OkObjectResult)query;
            string json = o.Value.ToString();
            var timeResult = JsonConvert.DeserializeObject<RetrieveTime>(json);
            if (timeResult == null) return BadRequest("Failed to retrieve current time.");
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            return Ok(new
            {
                From = kind,
                To = referenceKind,
                Rate = resultRate.Value,
                Now = timeResult.Now
            });
        }

        public class MoveDispenserBody
        {
            [DataMember(Name = "dispensers")]
            public List<int> Dispensers { get; set; }
        }

        [HttpPost("api/liquidity/{kind}/dispenser/{dispenserId}/moveDispensers")]
        public async Task<IActionResult> MoveDispenserToDispenserAsync(string kind, int dispenserId, [FromBody] MoveDispenserBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("dispenserId is required and must be greater than zero.");
            if (body == null) return BadRequest("moveDispenserBody is required");
            if (!body.Dispensers.Any()) return BadRequest("The dispensers list cannot be empty.");

            StringBuilder checkMoveDispensers = new StringBuilder();
            foreach (int toDispenserId in body.Dispensers)
            {
                checkMoveDispensers.AppendLine($@"
                    existToDispenser{toDispenserId} = liquid.Outlet.ExistDispenser({toDispenserId});
                    Check(existToDispenser{toDispenserId}) Error 'The request to dispenser id:{toDispenserId} does not exist.';
                    if (existToDispenser{toDispenserId})
                    {{
                        toDispenser{toDispenserId} = liquid.Outlet.FindDispenser({toDispenserId});
                        isToDispenserReady = toDispenser{toDispenserId}.Type == '{typeof(DispenserReady).Name}';
                        Check(isToDispenserReady) Error 'The request to dispenser id:{toDispenserId} is not ready to receive withdrawals.' + toDispenser{toDispenserId}.Type;
                        
                        validDispensers = dispenser.Id != toDispenser{toDispenserId}.Id;
                        Check(validDispensers) Error 'The request dispenser id:{dispenserId} and to dispenser id:{toDispenserId} must be different.';

                        containsRecursive = dispenser.ContainsRecursive(toDispenser{toDispenserId});
                        Check(!containsRecursive) Error 'The request dispenser id:{dispenserId} cannot move withdrawals to dispenser id:{toDispenserId} because it creates a recursive loop.';

                        inUseForAnyDispenser = liquid.Outlet.IsDispenserInUse(toDispenser{toDispenserId});
                        Check(!inUseForAnyDispenser) Error 'The request to dispenser id:{toDispenserId} is in use by a dispenser that has been committed.';
                    }}
                ");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The request dispenser id:{dispenserId} does not exist.';
                    if (existDispenser)
                    {{
                        dispenser = liquid.Outlet.FindDispenser({dispenserId});
                        isDispenserReady = dispenser.Type == '{typeof(DispenserReady).Name}';
                        Check(isDispenserReady) Error 'The request dispenser id:{dispenserId} is not ready to move withdrawals.' + dispenser.Type;
                        {checkMoveDispensers}
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});
                dispenser.MoveToDispensers({{{string.Join(",", body.Dispensers)}}});
            }}
            ");
            return result;
        }

        public class MoveTanksBody
        {
            [DataMember(Name = "tanks")]
            public List<int> Tanks { get; set; }
        }

        [HttpPost("api/liquidity/{kind}/tank/{tankId}/moveTanks")]
        public async Task<IActionResult> MoveTankToTankAsync(string kind, int tankId, [FromBody] MoveTanksBody MoveTanksBody)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (MoveTanksBody == null) return BadRequest("moveDepositsBody is required");

            StringBuilder checkMoveTanks = new StringBuilder();
            foreach (int toTankId in MoveTanksBody.Tanks)
            {
                checkMoveTanks.AppendLine($@"
                    existToTank{toTankId} = liquid.Source.ExistTank({toTankId});
                    Check(existToTank{toTankId}) Error 'The request to tank id:{toTankId} does not exist.';
                    if (existToTank{toTankId})
                    {{
                        toTank{toTankId} = liquid.Source.FindTank({toTankId});
                        isToTankReady = toTank{toTankId}.Type == '{typeof(TankReady).Name}';
                        Check(isToTankReady) Error 'The request to tank id:{toTankId} is not ready to receive deposits.' + toTank{toTankId}.Type;
                        validTanks = tank.Id != toTank{toTankId}.Id;

                        containsRecursive = tank.ContainsRecursive(toTank{toTankId});
                        Check(!containsRecursive) Error 'The request tank id:{tankId} cannot move deposits to tank id:{toTankId} because it creates a recursive loop.';
                        
                        inUseForAnyTanker = liquid.Source.IsTankInUse(toTank{toTankId});
                        Check(!inUseForAnyTanker) Error 'The request to tank id:{toTankId} is in use by a tanker';
                    }}
                ");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTank = liquid.Source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    if (existTank)
                    {{
                        tank = liquid.Source.FindTank({tankId});
                        isTankReady = tank.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'The request tank id:{tankId} is not ready to move deposits.' + tank.Type;
                        {checkMoveTanks}
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                tank.MoveToTanks({{{string.Join(",", MoveTanksBody.Tanks)}}});
            }}
            ");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tank/{tankId}/deposits/move")]
        public async Task<IActionResult> MoveDepositsToTankAsync(string kind, int tankId, [FromBody] MoveDepositsBody body)
        {

            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if(tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (body.Deposits == null || body.Deposits.Count == 0) return BadRequest("deposits is required");


            StringBuilder cmdMoveDeposits = new StringBuilder();
            var depositIds = string.Join(",", body.Deposits);
            cmdMoveDeposits.AppendLine($"tank = source.Jar.MoveToTank(itIsThePresent, Now, newJarVersion, {tankId}, {{{depositIds}}});");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    hasConfirmedDeposit = source.Jar.ConfirmedDeposits().Count() > 0;
				    Check(hasConfirmedDeposit) Error 'There are no confirm deposits for move a tank.';
                    exitsTank{tankId} = liquid.Source.ExistTank({tankId});
                    Check(exitsTank{tankId}) Error 'Tank id: {tankId} does not exist.';
                    if (exitsTank{tankId})
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'Tank id: {tankId} is not ready to move deposits.' + tank{tankId}.Type;
                    }}

                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('newJarVersion = '+source.NextJarVersion()+';');
                {cmdMoveDeposits}

            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/tank/move")]
        public async Task<IActionResult> MoveTankToTankerAsync(string kind, int tankerId, [FromBody] MoveTankBody body)
        {

            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (body.tankIds == null || body.tankIds.Count == 0) return BadRequest("deposits is required");

            StringBuilder checkTanks = new StringBuilder();
            foreach (int tankId in body.tankIds)
            {
                checkTanks.AppendLine($@"
                    exitsTank{tankId} = liquid.Source.ExistTank({tankId});
                    Check(exitsTank{tankId}) Error 'Tank id: {tankId} does not exist.';
                    if (exitsTank{tankId})
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankerReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankerReady) Error 'Tank id: {tankId} is not ready to create a tanker. status is ' + tank{tankId}.Type;

                        tankerId = liquid.Source.GetFirstTankerWith({tankId});
                        tankerIsValid = tankerId == -1;
                        Check(tankerIsValid) Error 'Tank id: {tankId} already exists in tanker with id ' + tankerId;
                    }}
                ");
            }

            StringBuilder cmdMoveContainer = new StringBuilder();
            var tankIds = string.Join(",", body.tankIds);
            cmdMoveContainer.AppendLine($"tank = source.MoveToTanker(itIsThePresent, Now, {tankerId}, {{{tankIds}}});");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    existTanker{tankerId} = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker{tankerId}) Error 'Tanker id: {tankerId} does not exist.';
                    if (existTanker{tankerId})
                    {{
                        {checkTanks}     
                    }}

                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                {cmdMoveContainer}

            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/deposits/move")]
        public async Task<IActionResult> MoveDepositsToTankerAsync(string kind, int tankerId, [FromBody] MoveDepositsBody body)
        {

            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (body.Deposits == null || body.Deposits.Count == 0) return BadRequest("deposits is required");


            StringBuilder cmdMoveDeposits = new StringBuilder();
            var depositIds = string.Join(",", body.Deposits);
            cmdMoveDeposits.AppendLine($"tank = source.Jar.MoveToTanker(itIsThePresent, Now, newJarVersion, {tankerId}, {{{depositIds}}});");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				Check(existKind) Error 'The request kind:{kind} does not exist.';

                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    source = liquid.Source;
                    hasConfirmedDeposit = source.Jar.ConfirmedDeposits().Count() > 0;
				    Check(hasConfirmedDeposit) Error 'There are no confirm deposits for move a tanker.';
                    existTanker{tankerId} = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker{tankerId}) Error 'Tanker id: {tankerId} does not exist.';
                    if (existTanker{tankerId})
                    {{
                        tanker{tankerId} = liquid.Source.FindTanker({tankerId});
                        isTankerReady = tanker{tankerId}.Type == '{typeof(TankerPending).Name}';
                        Check(isTankerReady) Error 'Tanker id: {tankerId} is not ready to move deposits.' + tanker{tankerId}.Type;
                    }}

                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;
                Eval('newJarVersion = '+source.NextJarVersion()+';');
                {cmdMoveDeposits}

            }}");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/deposit/{depositId}/detail")]
        public async Task<IActionResult> DepositDetailAsync(string kind, int depositId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var depositDataQuery = await RetriveDepositDataAsync(kind, depositId,resultRate.Value);
            if (!(depositDataQuery is OkObjectResult)) return depositDataQuery;
            OkObjectResult o = (OkObjectResult)depositDataQuery;
            string json = o.Value.ToString();
            var deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
            if (deposit == null) return BadRequest("Failed to retrieve deposit data.");

           ResponseDepositDetail depositDetail = new ResponseDepositDetail()
           {
               Id = deposit.Id,
               InvoiceId = deposit.InvoiceId,
               Amount = deposit.Amount,
               Destination = deposit.Destination,
               ExternalAtAddress = deposit.ExternalAtAddress,
               ExternalReference = deposit.ExternalReference,
               Rate = deposit.Rate,
               ConfirmedAmount = deposit.ConfirmedAmount,
               ConfirmedCurrency = deposit.ConfirmedCurrency,
               ReceivedAmount = deposit.ConfirmedAmount,
               ExchangeRateVariation = deposit.ExchangeRateVariation,
           };
            return Ok(depositDetail);
        }

        [HttpGet("api/liquidity/{kind}/tank/{tankId}/deposit/{depositId}/detail")]
        public async Task<IActionResult> DepositDetailFromTankAsync(string kind, int tankId, int depositId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (depositId <= 0) return BadRequest("depositId is required and must be greater than zero.");

            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");

            var depositQuery = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTank = liquid.source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    if( existTank )
                    {{
                        tank = liquid.Source.FindTank({tankId});
                        existDeposit = tank.ExistDeposit({depositId});
                        Check(existDeposit) Error 'The deposit:{depositId} does not exist in tank:{tankId}.';
                    }}  
                   
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                deposit = tank.FindDepositById({depositId});

                print deposit.Id id;
                print deposit.InvoiceId invoiceId;
                print deposit.Amount amount;
                print deposit.Rate rate;
                print deposit.ConfirmedAmount confirmedAmount;
                print deposit.ConfirmedCurrency confirmedCurrency;
                print deposit.Address depositAddress;
                print deposit.ExternalReference externalReference;
                print deposit.ExternalAtAddress externalAtAddress;
                print deposit.Destination destination;
                print deposit.ExchangeVariation({resultRate.Value}) exchangeRateVariation;
                print deposit.ValueDirectionAsString({resultRate.Value}) valueDirection;
            }}
            ");

            if (!(depositQuery is OkObjectResult)) return depositQuery;
            OkObjectResult o = (OkObjectResult)depositQuery;
            string json = o.Value.ToString();
            var deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
            if (deposit == null) return BadRequest("Failed to retrieve deposit data.");

            ResponseDepositDetail depositDetail = new ResponseDepositDetail()
            {
                Id = deposit.Id,
                InvoiceId = deposit.InvoiceId,
                Amount = deposit.Amount,
                Destination = deposit.Destination,
                ExternalAtAddress = deposit.ExternalAtAddress,
                ExternalReference = deposit.ExternalReference,
                Rate = deposit.Rate,
                ConfirmedAmount = deposit.ConfirmedAmount,
                ConfirmedCurrency = deposit.ConfirmedCurrency,
                ReceivedAmount = deposit.ConfirmedAmount,
                ExchangeRateVariation = deposit.ExchangeRateVariation,
                ValueDirection = deposit.ValueDirection,
                IntendedAmount = deposit.ConfirmedAmount

            };

            return Ok(depositDetail);
        }

        [HttpGet("api/liquidity/{kind}/deposits/summary")]
        public async Task<IActionResult> DepositsSummaryAsync(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, @$"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
            }}
            ", @$"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                source = liquid.Source;

                summary = source.BuildRecentDeposits(now);
                print summary.TotalConfirmedAmount totalConfirmedAmount;
                print summary.TotalLastHourConfirmed totalLastHourConfirmed;
                print summary.TotalPerHourConfirmed totalPerHourConfirmed;
                print summary.TotalCompletedAmount totalCompletedAmount;
                print summary.TotalPerHourCompleted totalPerHourCompleted;
                print summary.TotalLastHourCompletedAmount totalLastHourCompletedAmount;
                print summary.TotalTransactions totalTransactions;
                print summary.TotalTransactionsLastHour totalTransactionsLastHour;
                print summary.TotalTransactionsPerHour totalTransactionsPerHour;
                print summary.PeakHour peakHour;
                
            }}
            ");
            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/goalAmount")]
        public async Task<IActionResult> AddGoalAmountTankerAsync(string kind, int tankerId, [FromBody] GoalAmountBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");
            if(body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest("currencyCode is required.");
            if (body.GoalAmount <= 0) return BadRequest("goalAmount must be greater than zero.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if(existTanker)
                    {{
                        tanker{tankerId} = liquid.Source.FindTanker({tankerId});
                        isTankerReady = tanker{tankerId}.Type == '{typeof(TankerPending).Name}';
                        Check(isTankerReady) Error 'Tanker id: {tankerId} is not ready to set goalAmount.' + tanker{tankerId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tanker = liquid.Source.FindTanker({tankerId});
                tanker.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tank/{tankId}/goalAmount")]
        public async Task<IActionResult> AddGoalAmountTankAsync(string kind, int tankId, [FromBody] GoalAmountBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest("currencyCode is required.");
            if (body.GoalAmount <= 0) return BadRequest("goalAmount must be greater than zero.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTank = liquid.Source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    if(existTank)
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'Tank id: {tankId} is not ready to set goalAmount.' + tank{tankId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                tank.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/goalAmount/clear")]
        public async Task<IActionResult> RemoveGoalAmountTankerAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if(existTanker)
                    {{
                        tanker{tankerId} = liquid.Source.FindTanker({tankerId});
                        isTankerReady = tanker{tankerId}.Type == '{typeof(TankerPending).Name}';
                        Check(isTankerReady) Error 'Tanker id: {tankerId} is not ready to set goalAmount.' + tanker{tankerId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tanker = liquid.Source.FindTanker({tankerId});
                tanker.ClearGoalAmunt();
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tank/{tankId}/goalAmount/clear")]
        public async Task<IActionResult> ClearGoalAmountTankAsync(string kind, int tankId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTank = liquid.Source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                    if(existTank)
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'Tank id: {tankId} is not ready to set goalAmount.' + tank{tankId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                tank.ClearGoalAmunt();
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/targetDate")]
        public async Task<IActionResult> ChangeTargetDateTankerAsync(string kind, int tankerId, [FromBody] TargetDateBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if(body.TargetDate == null) return BadRequest($"Parameter {nameof(body.TargetDate)} is required");
            if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
            {
                return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
            }

            if(targetDate == DateTime.MinValue)
            {
                return BadRequest($"Parameter {nameof(body.TargetDate)} cannot be the minimum date value.");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});
                    Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if(existTanker)
                    {{

                        tanker{tankerId} = liquid.Source.FindTanker({tankerId});
                        isTankerPendy = tanker{tankerId}.Type == '{typeof(TankerPending).Name}';
                        Check(isTankerPendy) Error 'Tanker id: {tankerId} is not ready to set targetDate.' + tanker{tankerId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tanker = liquid.Source.FindTanker({tankerId});
                tanker.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/tank/{tankId}/targetDate")]
        public async Task<IActionResult> ChangeTargetDateTankAsync(string kind, int tankId, [FromBody] TargetDateBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest("tankId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (body.TargetDate == null) return BadRequest($"Parameter {nameof(body.TargetDate)} is required");
            if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
            {
                return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
            }

            if (targetDate == DateTime.MinValue)
            {
                return BadRequest($"Parameter {nameof(body.TargetDate)} cannot be the minimum date value.");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    isValidStartDate = liquid.Source.IsValidScheduledDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});
                    Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';
                    existTank = liquid.Source.ExistTank({tankId});
                    Check(existTank) Error 'The request tank id:{tankId} does not exist.';
                   
                    if(existTank)
                    {{
                        tank{tankId} = liquid.Source.FindTank({tankId});
                        isTankReady = tank{tankId}.Type == '{typeof(TankReady).Name}';
                        Check(isTankReady) Error 'Tank id: {tankId} is not ready to set tagetDate.' + tank{tankId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tank = liquid.Source.FindTank({tankId});
                tank.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/dispenser/{dispenserId}/name")]
        public async Task<IActionResult> UpdateDispenserNameAsync(string kind, int dispenserId, [FromBody] UpdateNameBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("dispenserId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest("name is required");

            string escapedName = Validator.StringEscape(body.Name);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existkind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser id:{dispenserId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});                
                dispenser.Name = '{escapedName}';
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/dispenser/{dispenserId}/description")]
        public async Task<IActionResult> UpdateDispenserDescriptionAsync(string kind, int dispenserId, [FromBody] UpdateDescriptionBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("dispenserId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest("name is required");

            string escapedDescription = Validator.StringEscape(body.Description);

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existkind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser id:{dispenserId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});                
                dispenser.Description = '{escapedDescription}';
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/dispenser/{dispenserId}/goalAmount")]
        public async Task<IActionResult> AddGoalAmountDispenserAsync(string kind, int dispenserId, [FromBody] GoalAmountBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("dispenserId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest("currencyCode is required.");
            if (body.GoalAmount <= 0) return BadRequest("goalAmount must be greater than zero.");

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existkind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser id:{dispenserId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});                
                dispenser.AddGoalAmount({body.GoalAmount},'{body.CurrencyCode}');
            }}");

            return result;
        }

        [HttpPut("api/liquidity/{kind}/dispenser/{dispenserId}/targetDate")]
        public async Task<IActionResult> AddTargetDateDispenserAsync(string kind, int dispenserId, [FromBody] TargetDateBody body)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest("dispenserId is required and must be greater than zero.");
            if (body == null) return BadRequest("body is required");
            if (body.TargetDate == null) return BadRequest($"Parameter {nameof(body.TargetDate)} is required");
            if (!DateTime.TryParse(body.TargetDate, out DateTime targetDate))
            {
                return BadRequest($"Parameter {nameof(body.TargetDate)} is not a valid date format.");
            }

            if (targetDate == DateTime.MinValue)
            {
                return BadRequest($"Parameter {nameof(body.TargetDate)} cannot be the minimum date value.");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if(existkind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    isValidStartDate = liquid.Source.IsValidScheduledDate(now,{targetDate.ToString("M/d/yyyy H:m:s")});
                    Check(!isValidStartDate) Error 'The request startDate:{targetDate.ToString("M/d/yyyy H:m:s")} must be a future date.';
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser id:{dispenserId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                dispenser = liquid.Outlet.FindDispenser({dispenserId});                
                dispenser.AddTargetDate(now, {targetDate.ToString("M/d/yyyy H:m:s")});
            }}");

            return result;
        }
        [HttpGet("api/liquidity/{kind}/withdrawal/{withdrawalId}/detail")]
        public async Task<IActionResult> WithdrawalDetailAsync(string kind, int withdrawalId)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("kind is required", nameof(kind));
            if (withdrawalId <= 0) throw new ArgumentException("withdrawalId must be greater than zero", nameof(withdrawalId));
            var resultRate = ExchangeRate.LastKnown[kind];
            if (!resultRate.IsSuccess) return NotFound($"Exchange rate from {kind} not found.");
            var withdrawalDetailQuery = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                    existWithdrawal = liquid.Outlet.ExistWithdrawal({withdrawalId});
                    Check(existWithdrawal) Error 'The withdrawal {withdrawalId} does not exist.';
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                withdrawal = liquid.Outlet.FindWithdrawal({withdrawalId});
                print withdrawal.Id withdrawalId;
                print withdrawal.Amount amount;
                print withdrawal.CreatedAt createdDate;
                print withdrawal.Reference reference;
                print withdrawal.ExternalReference externalReference;
                print withdrawal.Destination destination;
                print withdrawal.AtAddress atAddress;
                print withdrawal.ExchangeVariation({resultRate.Value}) exchangeRateVariation;
                print withdrawal.Rate rate;
                print 0.25 fee;
                print 0.30 spentAmount;	
                print 0.30 currentAmount;	
                print withdrawal.ReceivedAmount scheduledAmount;
                print withdrawal.ReceivedAmount enteredAmount;
                print withdrawal.ReceivedAmount intendedAmount;
                print now scheduledDate;	
            }}
            ");
            return withdrawalDetailQuery;
        }



        [HttpPut("api/liquidity/{kind}/tanker/{tankerId}/archive")]
        public async Task<IActionResult> ArchiveTankerAsync(string kind, int tankerId)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest("tankerId is required and must be greater than zero.");
            var result = await LiquidityAPI.Liquidity.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The request tanker id:{tankerId} does not exist.';
                    if(existTanker)
                    {{
                        tanker{tankerId} = liquid.Source.FindTanker({tankerId});
                        isTankerDispatched = tanker{tankerId}.Type == '{typeof(TankerDispatched).Name}';
                        Check(isTankerDispatched) Error 'Tanker id: {tankerId} is not ready to archive.' + tanker{tankerId}.Type;
                    }}
                }}
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                tanker = liquid.Source.FindTanker({tankerId});
                tanker.Archive();
            }}");
            return result;
        }

        [HttpGet("api/liquidity/validateaddress")]
        public async Task<IActionResult> ArchiveTankerAsync(string address)
        {
            if (string.IsNullOrWhiteSpace(address)) return BadRequest("address is required");

            var result = await PaymentManager.ValidAddressAsync(address);
            return Ok(result);
        }

        [HttpGet("api/liquidity/{kind}/tank/recents")]
        public async Task<IActionResult> TopFiveTankRecentsAsync( string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                allTanks = liquid.Source.RecentTanks();
                for (tanks : allTanks)
                {{
                    tank = tanks;
                    print tank.Id id;
                    print tank.Name name;
                    print tank.Description description;
                    print tank.IsInsideTanker() isInsideTanker;
                }}
                
            }}");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/tanker/recents")]
        public async Task<IActionResult> TopFiveTankerRecentsAsync(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                allTankers = liquid.Source.RecentTankers();
                for (tankers : allTankers)
                {{
                    tanker = tankers;
                    print tanker.Id id;
                    print tanker.Name name;
                    print tanker.Description description;
                }}
                
            }}");

            return result;
        }

        [HttpGet("api/liquidity/{kind}/dispenser/recents")]
        public async Task<IActionResult> TopFiveDispenserRecentsAsync(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
            }}
            ", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                allDispensers = liquid.Outlet.RecentDispensers();
                for (dispensers : allDispensers)
                {{
                    dispenser = dispensers;
                    print dispenser.Id id;
                    print dispenser.Name name;
                    print dispenser.Description description;
                }}
                
            }}");

            return result;
        }

        [HttpGet("{kind}/dispensers/{dispenserId}/compare/{version}")]
        public async Task<IActionResult> DispensersCompareAsync(string kind, int dispenserId, int version)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (dispenserId <= 0) return BadRequest($"{nameof(dispenserId)} {dispenserId} is not valid.");
            if (version < 0) return BadRequest($"{nameof(version)} {version} is not valid.");

            StringBuilder dispenserCheck = new StringBuilder();
            StringBuilder dispenserQuery = new StringBuilder();
            dispenserQuery.AppendLine($"dispenser = liquid.Outlet.FindDispenser({dispenserId});");
            if (version > 0)
            {
                dispenserQuery.AppendLine($"dispenser = dispenser.FindVersion({version});");
                dispenserCheck.AppendLine($"hasVersion = dispenser.HasVersion({version});");
                dispenserCheck.AppendLine($"Check(hasVersion) Error 'The dispenser {dispenserId} does not have version {version}.';");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existDispenser = liquid.Outlet.ExistDispenser({dispenserId});
                    Check(existDispenser) Error 'The dispenser {dispenserId} does not exist.';
                    if(existDispenser)
                    {{
                        dispenser = liquid.Outlet.FindDispenser({dispenserId});
                        {dispenserCheck}
                    }}
                }}
            }}", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                
                {dispenserQuery}

                print dispenser.Id dispenserId;
                print dispenser.Name dispenserName;
                print dispenser.Description dispenserDescription;
                print dispenser.Amount totalAmount;
                print dispenser.Version version;
                
                hasNext = dispenser.HasNext;
                print hasNext hasNext;
                if(hasNext)
                {{
                    print dispenser.Next.Version nextDispenserVersion;
                }}

                hasPrevious = dispenser.HasPrevious;
                print hasPrevious hasPrevious;
                if(hasPrevious)
                {{
                    print dispenser.Previous.Version previousDispenserVersion;
                }}

                isDispenserObsolete = liquid.Outlet.IsDispenserObsolete({dispenserId});
                print isDispenserObsolete isDispenserObsolete;
                if(!isDispenserObsolete)
                {{
                    withdrawalCount = 0;
                    for(transactions : dispenser.ExplandedWithdrawals)
                    {{
                        withdrawal = transactions;
                        print withdrawal.Id id;
                        print withdrawal.CreatedAt createdAt;
                        print withdrawal.Domain.Id domainId;
                        print withdrawal.ExternalAtAddress externalAtAddress;
                        print withdrawal.Amount amount;
                        print withdrawal.ConfirmedCurrency confirmedCurrency;
                        print withdrawal.ConfirmedAmount confirmedAmount;
                        print withdrawal.Destination destination;
                        withdrawalCount = withdrawalCount + 1;
                    }}
                    print withdrawalCount withdrawalCount;

                    if (hasPrevious)
                    {{
                        previousDispenser = dispenser.Previous;
                        print previousDispenser.Id previousDispenserId;
                        print previousDispenser.Name previousDispenserName;
                        print previousDispenser.Description previousDispenserDescription;
                        print previousDispenser.Amount previousDispenserTotalAmount;
                        previousDispenserWithdrawalCount = 0;
                        for(previousDispenserTransactions : previousDispenser.ExplandedWithdrawals)
                        {{
                            withdrawal = previousDispenserTransactions;
                            print withdrawal.Id id;
                            print withdrawal.CreatedAt createdAt;
                            print withdrawal.Domain.Id domainId;
                            print withdrawal.ExternalAtAddress externalAtAddress;
                            print withdrawal.Amount amount;
                            print withdrawal.ConfirmedCurrency confirmedCurrency;
                            print withdrawal.ConfirmedAmount confirmedAmount;
                            print withdrawal.Destination destination;
                            previousDispenserWithdrawalCount = previousDispenserWithdrawalCount + 1;
                        }}
                        print previousDispenserWithdrawalCount previousDispenserWithdrawalCount;
                    }}
                }}
            }}");

            return result;
        }

        [HttpGet("{kind}/tankers/{tankerId}/compare/{version}")]
        public async Task<IActionResult> TankersCompareAsync(string kind, int tankerId, int version)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");
            if (version < 0) return BadRequest($"{nameof(version)} {version} is not valid.");

            StringBuilder tankerCheck = new StringBuilder();
            StringBuilder tankerQuery = new StringBuilder();
            tankerQuery.AppendLine($"tanker = liquid.Source.FindTanker({tankerId});");
            if (version > 0)
            {
                tankerQuery.AppendLine($"tanker = tanker.FindVersion({version});");
                tankerCheck.AppendLine($"hasVersion = tanker.HasVersion({version});");
                tankerCheck.AppendLine($"Check(hasVersion) Error 'The tanker {tankerId} does not have version {version}.';");
            }

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTanker = liquid.Source.ExistTanker({tankerId});
                    Check(existTanker) Error 'The tanker {tankerId} does not exist.';
                    if(existTanker)
                    {{
                        tanker = liquid.Source.FindTanker({tankerId});
                        {tankerCheck}
                    }}
                }}
            }}", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                
                {tankerQuery}

                print tanker.Id tankerId;
                print tanker.Description tankerDescription;
                print tanker.Amount totalAmount;
                print tanker.Version version;

                hasNext = tanker.HasNext;
                print hasNext hasNext;
                if(hasNext)
                {{
                    print tanker.Next.Version nextTankerVersion;
                }}

                hasPrevious = tanker.HasPrevious;
                print hasPrevious hasPrevious;
                if(hasPrevious)
                {{
                    print tanker.Previous.Version previousTankerVersion;
                }}

                isTankerObsolete = liquid.Source.IsTankerObsolete({tankerId});
                print isTankerObsolete isTankerObsolete;
                if(!isTankerObsolete)
                {{
                    depositCount = 0;
                    for(transactions : tanker.ExplandedDeposits)
                    {{
                        deposit = transactions;
                        print deposit.Id id;
                        print deposit.CreatedAt createdAt;
                        print deposit.Domain.Id domainId;
                        print deposit.ExternalAtAddress externalAtAddress;
                        print deposit.Amount amount;
                        print deposit.ConfirmedCurrency confirmedCurrency;
                        print deposit.ConfirmedAmount confirmedAmount;
                        print deposit.Destination destination;
                        depositCount = depositCount + 1;
                    }}
                    print depositCount depositCount;
                    if (hasPrevious)
                    {{
                        previousTanker = tanker.Previous;
                        print previousTanker.Id previousTankerId;
                        print previousTanker.Description previousTankerDescription;
                        print previousTanker.Amount previousTankerTotalAmount;
                        previousTankerDepositCount = 0;

                        for(previousTankerTransactions : previousTanker.ExplandedDeposits)
                        {{
                            deposit = previousTankerTransactions;
                            print deposit.Id id;
                            print deposit.CreatedAt createdAt;
                            print deposit.Domain.Id domainId;
                            print deposit.ExternalAtAddress externalAtAddress;
                            print deposit.Amount amount;
                            print deposit.ConfirmedCurrency confirmedCurrency;
                            print deposit.ConfirmedAmount confirmedAmount;
                            print deposit.Destination destination;
                            previousTankerDepositCount = previousTankerDepositCount + 1;
                        }}
                        print previousTankerDepositCount previousTankerDepositCount;
                    }}
                }}
            }}");
            return result;
        }

        [HttpGet("{kind}/tanks/{tankId}/compare/{version}")]
        public async Task<IActionResult> TanksCompareAsync(string kind, int tankId, int version)
        {
            if (string.IsNullOrWhiteSpace(kind)) return BadRequest("kind is required");
            if (tankId <= 0) return BadRequest($"{nameof(tankId)} {tankId} is not valid.");
            if (version < 0) return BadRequest($"{nameof(version)} {version} is not valid.");

            StringBuilder tankCheck = new StringBuilder();
            StringBuilder tankQuery = new StringBuilder();
            tankQuery.AppendLine($"tank = liquid.Source.FindTank({tankId});");
            if (version > 0)
            {
                tankQuery.AppendLine($"tank = tank.FindVersion({version});");
                tankCheck.AppendLine($"hasVersion = tank.HasVersion({version});");
                tankCheck.AppendLine($"Check(hasVersion) Error 'The tank {tankId} does not have version {version}.';");
            }            

            var result = await LiquidityAPI.Liquidity.PerformChkThenQryAsync(HttpContext, $@"
            {{
                existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                Check(existKind) Error 'The request kind:{kind} does not exist.';
                if (existKind)
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    existTank = liquid.Source.ExistTank({tankId});
                    Check(existTank) Error 'The tank {tankId} does not exist.';
                    if(existTank)
                    {{
                        tank = liquid.Source.FindTank({tankId});
                        {tankCheck}
                    }}
                }}
            }}", $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                
                {tankQuery}

                print tank.Id tankId;
                print tank.Description tankDescription;
                print tank.Amount totalAmount;
                print tank.Version version;

                hasNext = tank.HasNext;
                print hasNext hasNext;
                if(hasNext)
                {{
                    print tank.Next.Version nextTankVersion;
                }}

                hasPrevious = tank.HasPrevious;
                print hasPrevious hasPrevious;
                if(hasPrevious)
                {{
                    print tank.Previous.Version previousVersion;
                }}

                isTankObsolete = liquid.Source.IsTankObsolete({tankId});
                print isTankObsolete isTankObsolete;
                if(!isTankObsolete)
                {{
                    depositCount = 0;
                    for(transactions : tank.ExplandedDeposits)
                    {{
                        deposit = transactions;
                        print deposit.Id id;
                        print deposit.CreatedAt createdAt;
                        print deposit.Domain.Id domainId;
                        print deposit.ExternalAtAddress externalAtAddress;
                        print deposit.Amount amount;
                        print deposit.ConfirmedCurrency confirmedCurrency;
                        print deposit.ConfirmedAmount confirmedAmount;
                        print deposit.Destination destination;
                        depositCount = depositCount + 1;
                    }}
                    print depositCount depositCount;
                    
                    if (hasPrevious)
                    {{
                        previousTank = tank.Previous;

                        print previousTank.Id previousTankId;
                        print previousTank.Description previousTankDescription;
                        print previousTank.Amount previousTankTotalAmount;
                        previousTankDepositCount = 0;
                        for(previousTankTransactions : previousTank.ExplandedDeposits)
                        {{
                            deposit = previousTankTransactions;
                            print deposit.Id id;
                            print deposit.CreatedAt createdAt;
                            print deposit.Domain.Id domainId;
                            print deposit.ExternalAtAddress externalAtAddress;
                            print deposit.Amount amount;
                            print deposit.ConfirmedCurrency confirmedCurrency;
                            print deposit.ConfirmedAmount confirmedAmount;
                            print deposit.Destination destination;
                            previousTankDepositCount = previousTankDepositCount + 1;
                        }}
                        print previousTankDepositCount previousTankDepositCount;
                    }}
                }}
            }}");

            return result;
        }
        
        [DataContract(Name = "TargetDateBody")]
        public class TargetDateBody
        {
            [DataMember(Name = "targetDate")]
            public string TargetDate { get; set; } 
        }


        public class GoalAmountBody
        {
           public decimal GoalAmount { get; set; }
           public string CurrencyCode { get; set; }

        }

        public class MoveDepositsBody
        {
            public List<int> Deposits { get; set; }
        }

        public class MoveTankBody
        {
            public List<int> tankIds { get; set; }
        }

        [DataContract(Name = "CreateTankBody")]
        public class CreateTankBody
        {
            [DataMember(Name = "deposits")]
            public List<int>? Deposits { get; set; }

            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "color")]
            public string? Color { get; set; }
            [DataMember(Name = "targetAmount")]
            public decimal? GoalAmount { get; set; }
            [DataMember(Name = "currencyCode")]
            public string? CurrencyCode { get; set; } = $"{Currencies.CODES.USD}";
            [DataMember(Name = "targetDate")]
            public string? TargetDate { get; set; }
        }

        [DataContract(Name = "CreateTankWithTankBody")]
        public class CreateTankWithTankBody: CreateTankBody
        {
            [DataMember(Name = "tankIds")]
            public List<int> TankIds { get; set; }
        }

            [DataContract(Name = "CreateTankerBody")]
        public class CreateTankerBody
        {
            [DataMember(Name = "tanks")]
            public List<int>? Tanks { get; set; }

            [DataMember(Name = "name")]
            public string Name { get; set; }

            [DataMember(Name = "description")]
            public string Description { get; set; }

            [DataMember(Name = "color")]
            public string? Color { get; set; }
            public decimal? GoalAmount { get; set; }
            [DataMember(Name = "currencyCode")]
            public string? CurrencyCode { get; set; } = $"{Currencies.CODES.USD}";
            [DataMember(Name = "targetDate")]
            public string? TargetDate { get; set; }
        }

        [DataContract(Name = "CreateTankerBody")]
        public class CreateTankerWithDepositBody
        {
            [DataMember(Name = "depositIds")]
            public List<int> DepositIds { get; set; }

            [DataMember(Name = "name")]
            public string Name { get; set; }

            [DataMember(Name = "description")]
            public string Description { get; set; }

            [DataMember(Name = "color")]
            public string? Color { get; set; }

            public decimal? GoalAmount { get; set; }
            [DataMember(Name = "currencyCode")]
            public string? CurrencyCode { get; set; } = $"{Currencies.CODES.USD}";
            [DataMember(Name = "targetDate")]
            public string? TargetDate { get; set; }

        }

        [DataContract(Name = "MergeTankBody")]
        public class MergeTankBody
        {
            [DataMember(Name = "fromTankId")]
            public int FromTankId { get; set; }

            [DataMember(Name = "tanks")]
            public List<int> Tanks { get; set; }

            [DataMember(Name = "name")]
            public string Name { get; set; }

            [DataMember(Name = "description")]
            public string Description { get; set; }
        }

        [DataContract(Name = "UpdateNameBody")]
        public class UpdateNameBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
        }

        [DataContract(Name = "UpdateDescriptionBody")]
        public class UpdateDescriptionBody
        {
            [DataMember(Name = "description")]
            public string Description { get; set; }
        } 
        
        [DataContract(Name = "BodydColor")]
        public class BodydColor
        {
            [DataMember(Name = "newColor")]
            public string NewColor { get; set; }
        }

        public class CsvDepositData
        {
            [JsonProperty("amount")]
            public decimal Amount { get; set; }
            [JsonProperty("address")]
            public string Address { get; set; }
        }

        public class CsvTankData
        {
            [JsonProperty("deposits")]
            public List<CsvDepositData> Deposits { get; set; }
        }

        public class CsvTankerData
        {
            [JsonProperty("description")]
            public string Description { get; set; }
            [JsonProperty("name")]
            public string Name { get; set; }
            [JsonProperty("tanks")]
            public List<CsvTankData> Tanks { get; set; }
        }


    }
}
